# 家具定制服务全周期管理平台

个性化家具定制服务全周期管理平台前端应用，基于 React + TypeScript + Ant Design 开发。

## 🚀 功能特性

### 客户端功能
- **工作台**：个人数据概览和快速操作
- **需求管理**：提交和管理定制需求
- **设计方案**：查看和确认设计方案
- **项目进度**：实时跟踪项目进展
- **交付验收**：在线验收交付成果
- **客户服务**：工单系统和知识库

### 管理端功能
- **管理工作台**：业务数据总览
- **客户管理**：客户信息和关系管理
- **需求管理**：需求分配和处理
- **方案管理**：设计方案创建和管理
- **项目管理**：项目规划和进度控制
- **交付管理**：交付物管理和质量控制
- **客户服务**：工单处理和满意度管理
- **知识库**：FAQ和帮助文档管理
- **数据分析**：业务数据分析和报告
- **用户管理**：内部用户和权限管理
- **系统设置**：系统配置和参数设置

## 🛠 技术栈

- **前端框架**：React 18 + TypeScript
- **UI组件库**：Ant Design 5.x
- **状态管理**：Redux Toolkit
- **路由管理**：React Router 6
- **HTTP客户端**：Axios
- **构建工具**：Create React App
- **代码规范**：ESLint + TypeScript

## 📦 项目结构

```
src/
├── components/          # 公共组件
│   └── common/         # 通用组件
├── constants/          # 常量定义
├── data/              # 模拟数据
├── hooks/             # 自定义Hooks
├── layouts/           # 布局组件
├── pages/             # 页面组件
│   ├── auth/          # 认证页面
│   ├── customer/      # 客户端页面
│   └── admin/         # 管理端页面
├── services/          # API服务
├── store/             # Redux状态管理
│   └── slices/        # Redux切片
├── types/             # TypeScript类型定义
└── utils/             # 工具函数
```

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm start
```

应用将在 http://localhost:3000 启动

### 构建生产版本
```bash
npm run build
```

### 运行测试
```bash
npm test
```

## 🔐 演示账号

### 客户端
- 用户名：demo_customer
- 密码：demo123

### 管理端
- 用户名：admin
- 密码：admin123

## 📱 功能模块

### 1. 用户认证系统
- 登录/注册
- 权限控制
- 角色管理
- 会话管理

### 2. 客户需求管理
- 需求提交表单
- 需求状态跟踪
- 智能需求分析
- 文件附件上传

### 3. 方案设计系统
- 可视化方案展示
- 组件化设计
- 成本计算
- 协作评论

### 4. 项目管理系统
- 甘特图展示
- 里程碑管理
- 任务分配
- 进度跟踪

### 5. 交付验收系统
- 交付物管理
- 在线验收
- 质量检查
- 问题反馈

### 6. 客户服务系统
- 工单管理
- 知识库
- 满意度调查
- NPS评分

## 🎨 设计规范

### 色彩规范
- 主色：#1890ff (蓝色)
- 成功：#52c41a (绿色)
- 警告：#faad14 (橙色)
- 错误：#f5222d (红色)

### 组件规范
- 统一使用 Ant Design 组件
- 自定义组件遵循 Ant Design 设计语言
- 响应式设计，支持移动端

## 🔧 开发规范

### 代码规范
- 使用 TypeScript 进行类型检查
- 遵循 ESLint 代码规范
- 组件使用函数式组件 + Hooks
- 状态管理使用 Redux Toolkit

### 文件命名
- 组件文件使用 PascalCase
- 工具函数使用 camelCase
- 常量使用 UPPER_SNAKE_CASE

### Git 提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 📈 性能优化

- 路由懒加载
- 组件按需加载
- 图片懒加载
- 接口请求缓存
- 虚拟滚动（大数据列表）

## 🔒 安全特性

- JWT Token 认证
- 路由权限控制
- API 接口鉴权
- XSS 防护
- CSRF 防护

## 🌐 浏览器支持

- Chrome >= 88
- Firefox >= 85
- Safari >= 14
- Edge >= 88

## 📄 许可证

MIT License

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📞 联系我们

如有问题或建议，请联系开发团队。
