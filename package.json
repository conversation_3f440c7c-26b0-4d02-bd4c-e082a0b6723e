{"name": "furniture-customization-platform", "version": "1.0.0", "description": "个性化家具定制服务全周期管理平台", "private": true, "dependencies": {"@reduxjs/toolkit": "^2.0.1", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "antd": "^5.12.8", "axios": "^1.6.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^9.0.4", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "typescript": "^5.3.3", "web-vitals": "^3.5.0", "@ant-design/icons": "^5.2.6", "dayjs": "^1.11.10", "recharts": "^2.8.0", "socket.io-client": "^4.7.4", "react-query": "^3.39.3", "react-beautiful-dnd": "^13.1.1", "react-gantt-chart": "^0.3.9", "file-saver": "^2.0.5", "@types/file-saver": "^2.0.7", "html2canvas": "^1.4.1", "jspdf": "^2.5.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-beautiful-dnd": "^13.1.8", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0"}, "proxy": "http://localhost:8080"}