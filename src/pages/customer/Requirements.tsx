import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Input,
  Select,
  Modal,
  Form,
  DatePicker,
  Upload,
  message,
  Row,
  Col,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import { useAppSelector, useAppDispatch } from '@/store';
import { mockRequirements, furnitureCategories } from '@/data/mockData';
import { FURNITURE_CATEGORIES, BUDGET_RANGES, STATUS_COLORS } from '@/constants';
import type { Requirement, RequirementFormData } from '@/types';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const Requirements: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [form] = Form.useForm();
  
  const [requirements, setRequirements] = useState<Requirement[]>(mockRequirements);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingRequirement, setEditingRequirement] = useState<Requirement | null>(null);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');

  const { user } = useAppSelector((state) => state.auth);

  useEffect(() => {
    // 如果有ID参数，显示详情模态框
    if (id) {
      const requirement = requirements.find(r => r.id === parseInt(id));
      if (requirement) {
        setEditingRequirement(requirement);
        setModalVisible(true);
      }
    }
  }, [id, requirements]);

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      SUBMITTED: '已提交',
      ANALYZING: '分析中',
      ASSIGNED: '已分配',
      IN_PROGRESS: '进行中',
      COMPLETED: '已完成',
    };
    return statusMap[status] || status;
  };

  const getCategoryText = (category: string) => {
    const categoryMap: Record<string, string> = {
      LIVING_ROOM: '客厅家具',
      BEDROOM: '卧室家具',
      KITCHEN: '厨房家具',
      DINING_ROOM: '餐厅家具',
      OFFICE: '办公家具',
      BATHROOM: '浴室家具',
      OUTDOOR: '户外家具',
      CUSTOM: '定制家具',
    };
    return categoryMap[category] || category;
  };

  const columns: ColumnsType<Requirement> = [
    {
      title: '需求标题',
      dataIndex: 'title',
      key: 'title',
      render: (text, record) => (
        <a onClick={() => handleView(record)}>{text}</a>
      ),
    },
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
      render: (category) => getCategoryText(category),
    },
    {
      title: '预算范围',
      dataIndex: 'budgetRange',
      key: 'budgetRange',
      render: (range) => {
        const budget = BUDGET_RANGES.find(b => b.value === range);
        return budget ? budget.label : range;
      },
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority) => {
        const colorMap: Record<string, string> = {
          LOW: 'green',
          MEDIUM: 'orange',
          HIGH: 'red',
        };
        const textMap: Record<string, string> = {
          LOW: '低',
          MEDIUM: '中',
          HIGH: '高',
        };
        return <Tag color={colorMap[priority]}>{textMap[priority]}</Tag>;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={STATUS_COLORS[status as keyof typeof STATUS_COLORS]}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '期望交付',
      dataIndex: 'expectedDelivery',
      key: 'expectedDelivery',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => date.split('T')[0],
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          {record.status === 'SUBMITTED' && (
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            >
              编辑
            </Button>
          )}
        </Space>
      ),
    },
  ];

  const handleView = (requirement: Requirement) => {
    setEditingRequirement(requirement);
    setModalVisible(true);
    navigate(`/customer/requirements/${requirement.id}`);
  };

  const handleEdit = (requirement: Requirement) => {
    setEditingRequirement(requirement);
    form.setFieldsValue({
      ...requirement,
      expectedDelivery: requirement.expectedDelivery,
    });
    setModalVisible(true);
  };

  const handleAdd = () => {
    setEditingRequirement(null);
    form.resetFields();
    setModalVisible(true);
    navigate('/customer/requirements');
  };

  const handleModalClose = () => {
    setModalVisible(false);
    setEditingRequirement(null);
    form.resetFields();
    navigate('/customer/requirements');
  };

  const handleSubmit = async (values: RequirementFormData) => {
    try {
      setLoading(true);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (editingRequirement) {
        // 更新需求
        const updatedRequirements = requirements.map(req =>
          req.id === editingRequirement.id
            ? { ...req, ...values, updatedAt: new Date().toISOString() }
            : req
        );
        setRequirements(updatedRequirements);
        message.success('需求更新成功！');
      } else {
        // 创建新需求
        const newRequirement: Requirement = {
          id: Date.now(),
          customerId: user?.id || 1,
          ...values,
          status: 'SUBMITTED',
          tags: [],
          attachments: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        } as Requirement;
        
        setRequirements([newRequirement, ...requirements]);
        message.success('需求提交成功！');
      }
      
      handleModalClose();
    } catch (error) {
      message.error('操作失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const filteredRequirements = requirements.filter(req => {
    const matchesSearch = !searchText || 
      req.title.toLowerCase().includes(searchText.toLowerCase()) ||
      req.description.toLowerCase().includes(searchText.toLowerCase());
    const matchesStatus = !statusFilter || req.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="page-container">
      <div className="page-header">
        <Title level={3} className="page-title">我的需求</Title>
        <Text className="page-description">
          管理您的家具定制需求，跟踪需求处理进度
        </Text>
      </div>

      <Card>
        <div className="table-header">
          <Space>
            <Input
              placeholder="搜索需求标题或描述"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 250 }}
            />
            <Select
              placeholder="筛选状态"
              value={statusFilter}
              onChange={setStatusFilter}
              allowClear
              style={{ width: 150 }}
            >
              <Option value="SUBMITTED">已提交</Option>
              <Option value="ANALYZING">分析中</Option>
              <Option value="ASSIGNED">已分配</Option>
              <Option value="IN_PROGRESS">进行中</Option>
              <Option value="COMPLETED">已完成</Option>
            </Select>
          </Space>
          
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            提交新需求
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={filteredRequirements}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredRequirements.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 需求详情/编辑模态框 */}
      <Modal
        title={editingRequirement ? '需求详情' : '提交新需求'}
        open={modalVisible}
        onCancel={handleModalClose}
        footer={null}
        width={800}
        destroyOnClose
      >
        {editingRequirement && editingRequirement.status !== 'SUBMITTED' ? (
          // 只读模式
          <div>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Text strong>需求标题：</Text>
                <div>{editingRequirement.title}</div>
              </Col>
              <Col span={12}>
                <Text strong>类别：</Text>
                <div>{getCategoryText(editingRequirement.category)}</div>
              </Col>
              <Col span={12}>
                <Text strong>预算范围：</Text>
                <div>{BUDGET_RANGES.find(b => b.value === editingRequirement.budgetRange)?.label}</div>
              </Col>
              <Col span={12}>
                <Text strong>期望交付：</Text>
                <div>{editingRequirement.expectedDelivery}</div>
              </Col>
              <Col span={24}>
                <Text strong>需求描述：</Text>
                <div style={{ marginTop: 8, padding: 12, background: '#f5f5f5', borderRadius: 6 }}>
                  {editingRequirement.description}
                </div>
              </Col>
              <Col span={24}>
                <Text strong>当前状态：</Text>
                <div style={{ marginTop: 8 }}>
                  <Tag color={STATUS_COLORS[editingRequirement.status as keyof typeof STATUS_COLORS]}>
                    {getStatusText(editingRequirement.status)}
                  </Tag>
                </div>
              </Col>
            </Row>
          </div>
        ) : (
          // 编辑模式
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
          >
            <Form.Item
              name="title"
              label="需求标题"
              rules={[{ required: true, message: '请输入需求标题' }]}
            >
              <Input placeholder="请简要描述您的需求" />
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="category"
                  label="家具类别"
                  rules={[{ required: true, message: '请选择家具类别' }]}
                >
                  <Select placeholder="请选择家具类别">
                    {Object.entries(FURNITURE_CATEGORIES).map(([key, value]) => (
                      <Option key={key} value={value}>
                        {getCategoryText(value)}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="budgetRange"
                  label="预算范围"
                  rules={[{ required: true, message: '请选择预算范围' }]}
                >
                  <Select placeholder="请选择预算范围">
                    {BUDGET_RANGES.map(range => (
                      <Option key={range.value} value={range.value}>
                        {range.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="expectedDelivery"
                  label="期望交付时间"
                  rules={[{ required: true, message: '请选择期望交付时间' }]}
                >
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="priority"
                  label="优先级"
                  rules={[{ required: true, message: '请选择优先级' }]}
                >
                  <Select placeholder="请选择优先级">
                    <Option value="LOW">低</Option>
                    <Option value="MEDIUM">中</Option>
                    <Option value="HIGH">高</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="description"
              label="详细描述"
              rules={[{ required: true, message: '请输入详细描述' }]}
            >
              <TextArea
                rows={4}
                placeholder="请详细描述您的需求，包括风格偏好、功能要求、材料偏好等"
              />
            </Form.Item>

            <Form.Item
              name="attachments"
              label="附件"
            >
              <Upload
                multiple
                beforeUpload={() => false}
                listType="text"
              >
                <Button icon={<UploadOutlined />}>上传参考图片或文档</Button>
              </Upload>
            </Form.Item>

            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit" loading={loading}>
                  {editingRequirement ? '更新需求' : '提交需求'}
                </Button>
                <Button onClick={handleModalClose}>
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        )}
      </Modal>
    </div>
  );
};

export default Requirements;
