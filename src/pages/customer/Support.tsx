import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Input,
  Select,
  Modal,
  Form,
  Row,
  Col,
  Tabs,
  List,
  Avatar,
  Rate,
  Divider,
  Collapse,
  Alert,
  Timeline,
  message,
  Tooltip,
  Badge,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  MessageOutlined,
  QuestionCircleOutlined,
  CustomerServiceOutlined,
  PhoneOutlined,
  MailOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  StarOutlined,
} from '@ant-design/icons';
import { useAppSelector } from '@/store';
import { mockSupportTickets, mockKnowledgeArticles, mockSatisfactionSurveys } from '@/data/mockData';
import { STATUS_COLORS } from '@/constants';
import type { SupportTicket, KnowledgeArticle, SatisfactionSurvey } from '@/types';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;
const { Panel } = Collapse;

const Support: React.FC = () => {
  const [form] = Form.useForm();
  const [surveyForm] = Form.useForm();

  const [tickets, setTickets] = useState<SupportTicket[]>(mockSupportTickets);
  const [knowledgeArticles] = useState<KnowledgeArticle[]>(mockKnowledgeArticles);
  const [surveys, setSurveys] = useState<SatisfactionSurvey[]>(mockSatisfactionSurveys);
  const [loading, setLoading] = useState(false);
  const [ticketModalVisible, setTicketModalVisible] = useState(false);
  const [surveyModalVisible, setSurveyModalVisible] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState<SupportTicket | null>(null);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [activeTab, setActiveTab] = useState('tickets');

  const { user } = useAppSelector((state) => state.auth);

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      OPEN: '待处理',
      IN_PROGRESS: '处理中',
      RESOLVED: '已解决',
      CLOSED: '已关闭',
    };
    return statusMap[status] || status;
  };

  const getPriorityText = (priority: string) => {
    const priorityMap: Record<string, string> = {
      LOW: '低',
      MEDIUM: '中',
      HIGH: '高',
      URGENT: '紧急',
    };
    return priorityMap[priority] || priority;
  };

  const ticketColumns: ColumnsType<SupportTicket> = [
    {
      title: '工单编号',
      dataIndex: 'id',
      key: 'id',
      render: (id, record) => (
        <a onClick={() => handleViewTicket(record)}>
          TK-{String(id).padStart(4, '0')}
        </a>
      ),
    },
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
    },
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority) => {
        const colorMap: Record<string, string> = {
          LOW: 'green',
          MEDIUM: 'orange',
          HIGH: 'red',
          URGENT: 'red',
        };
        return (
          <Tag color={colorMap[priority]} style={{
            fontWeight: priority === 'URGENT' ? 'bold' : 'normal'
          }}>
            {getPriorityText(priority)}
          </Tag>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={STATUS_COLORS[status as keyof typeof STATUS_COLORS]}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => date.split('T')[0],
      sorter: (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="查看详情">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleViewTicket(record)}
            />
          </Tooltip>
          {record.status !== 'CLOSED' && (
            <Tooltip title="添加回复">
              <Button
                type="link"
                icon={<MessageOutlined />}
                onClick={() => handleReplyTicket(record)}
              />
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  const handleCreateTicket = () => {
    setSelectedTicket(null);
    form.resetFields();
    setTicketModalVisible(true);
  };

  const handleViewTicket = (ticket: SupportTicket) => {
    setSelectedTicket(ticket);
    // 这里可以打开详情模态框或跳转到详情页
    message.info('工单详情功能开发中...');
  };

  const handleReplyTicket = (ticket: SupportTicket) => {
    setSelectedTicket(ticket);
    message.info('工单回复功能开发中...');
  };

  const handleSubmitTicket = async (values: any) => {
    try {
      setLoading(true);

      const newTicket: SupportTicket = {
        id: Date.now(),
        customerId: user!.id,
        customer: {
          id: user!.id,
          name: user!.fullName,
          email: user!.email,
          phone: user!.phone || '',
          industry: '',
          level: 'STANDARD',
          tags: [],
          createdAt: '',
          updatedAt: '',
        },
        ...values,
        status: 'OPEN' as const,
        createdAt: new Date().toISOString(),
      };

      setTickets([newTicket, ...tickets]);
      setTicketModalVisible(false);
      message.success('工单提交成功！我们会尽快处理您的问题。');
    } catch (error) {
      message.error('提交失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitSurvey = async (values: any) => {
    try {
      setLoading(true);

      const newSurvey: SatisfactionSurvey = {
        id: Date.now(),
        projectId: values.projectId,
        customerId: user!.id,
        surveyType: 'CSAT',
        score: values.score,
        feedback: values.feedback,
        surveyDate: new Date().toISOString(),
        responseDate: new Date().toISOString(),
      };

      setSurveys([newSurvey, ...surveys]);
      setSurveyModalVisible(false);
      message.success('感谢您的反馈！');
    } catch (error) {
      message.error('提交失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const filteredTickets = tickets.filter(ticket => {
    const matchesSearch = !searchText ||
      ticket.title.toLowerCase().includes(searchText.toLowerCase()) ||
      ticket.description.toLowerCase().includes(searchText.toLowerCase());
    const matchesStatus = !statusFilter || ticket.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const faqData = [
    {
      key: '1',
      label: '如何提交定制需求？',
      children: (
        <div>
          <p>您可以通过以下步骤提交定制需求：</p>
          <ol>
            <li>登录系统后，进入"我的需求"页面</li>
            <li>点击"提交新需求"按钮</li>
            <li>填写详细的需求信息，包括家具类型、预算范围、期望交付时间等</li>
            <li>上传参考图片或文档（可选）</li>
            <li>提交后，我们的设计师会在2个工作日内联系您</li>
          </ol>
        </div>
      ),
    },
    {
      key: '2',
      label: '设计方案确认后可以修改吗？',
      children: (
        <div>
          <p>方案确认后的修改政策：</p>
          <ul>
            <li>确认后24小时内：可以免费修改</li>
            <li>生产开始前：可以修改，但可能产生额外费用</li>
            <li>生产开始后：原则上不允许修改，特殊情况需要重新评估</li>
          </ul>
          <p>建议您在确认前仔细检查所有细节。</p>
        </div>
      ),
    },
    {
      key: '3',
      label: '交付时间如何确定？',
      children: (
        <div>
          <p>交付时间取决于以下因素：</p>
          <ul>
            <li>家具复杂程度和定制要求</li>
            <li>材料采购周期</li>
            <li>生产排期</li>
            <li>物流配送时间</li>
          </ul>
          <p>一般情况下，简单定制需要2-4周，复杂定制需要4-8周。具体时间会在方案确认时告知。</p>
        </div>
      ),
    },
    {
      key: '4',
      label: '如何跟踪项目进度？',
      children: (
        <div>
          <p>您可以通过以下方式跟踪项目进度：</p>
          <ul>
            <li>登录系统查看"项目进度"页面</li>
            <li>查看实时的里程碑完成情况</li>
            <li>接收系统自动发送的进度通知</li>
            <li>直接联系项目经理了解详情</li>
          </ul>
        </div>
      ),
    },
    {
      key: '5',
      label: '验收时发现问题怎么办？',
      children: (
        <div>
          <p>如果在验收时发现问题：</p>
          <ol>
            <li>在验收系统中详细记录问题</li>
            <li>上传问题照片作为证据</li>
            <li>标注问题的严重程度</li>
            <li>我们会在24小时内响应并制定解决方案</li>
            <li>问题解决后会重新安排验收</li>
          </ol>
        </div>
      ),
    },
  ];

  return (
    <div className="page-container">
      <div className="page-header">
        <Title level={3} className="page-title">客户服务</Title>
        <Text className="page-description">
          获取专业的客户支持，查看常见问题解答，提交服务工单
        </Text>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab={<span><CustomerServiceOutlined />我的工单</span>} key="tickets">
          <Card>
            <div className="table-header">
              <Space>
                <Input
                  placeholder="搜索工单标题或描述"
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  style={{ width: 250 }}
                />
                <Select
                  placeholder="筛选状态"
                  value={statusFilter}
                  onChange={setStatusFilter}
                  allowClear
                  style={{ width: 150 }}
                >
                  <Option value="OPEN">待处理</Option>
                  <Option value="IN_PROGRESS">处理中</Option>
                  <Option value="RESOLVED">已解决</Option>
                  <Option value="CLOSED">已关闭</Option>
                </Select>
              </Space>

              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleCreateTicket}
              >
                提交工单
              </Button>
            </div>

            <Table
              columns={ticketColumns}
              dataSource={filteredTickets}
              rowKey="id"
              loading={loading}
              pagination={{
                total: filteredTickets.length,
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
            />
          </Card>
        </TabPane>

        <TabPane tab={<span><QuestionCircleOutlined />常见问题</span>} key="faq">
          <Card>
            <Collapse accordion>
              {faqData.map(item => (
                <Panel header={item.label} key={item.key}>
                  {item.children}
                </Panel>
              ))}
            </Collapse>
          </Card>
        </TabPane>

        <TabPane tab={<span><StarOutlined />满意度调查</span>} key="survey">
          <Row gutter={[16, 16]}>
            <Col span={16}>
              <Card title="历史调查记录">
                <List
                  dataSource={surveys}
                  renderItem={(survey) => (
                    <List.Item>
                      <List.Item.Meta
                        title={
                          <Space>
                            <Text>项目满意度调查</Text>
                            <Rate disabled value={survey.score} />
                            <Text type="secondary">({survey.score}/5)</Text>
                          </Space>
                        }
                        description={
                          <Space direction="vertical" size={4}>
                            {survey.feedback && (
                              <Text type="secondary">{survey.feedback}</Text>
                            )}
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              调查时间：{survey.surveyDate.split('T')[0]}
                            </Text>
                          </Space>
                        }
                      />
                    </List.Item>
                  )}
                />
              </Card>
            </Col>

            <Col span={8}>
              <Card title="提交新的满意度调查">
                <Button
                  type="primary"
                  icon={<StarOutlined />}
                  block
                  onClick={() => setSurveyModalVisible(true)}
                >
                  参与满意度调查
                </Button>
                <Divider />
                <Text type="secondary">
                  您的反馈对我们非常重要，帮助我们不断改进服务质量。
                </Text>
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab={<span><PhoneOutlined />联系我们</span>} key="contact">
          <Row gutter={[24, 24]}>
            <Col span={12}>
              <Card title="联系方式">
                <Space direction="vertical" size={16} style={{ width: '100%' }}>
                  <div>
                    <Title level={5}>
                      <PhoneOutlined /> 客服热线
                    </Title>
                    <Text>************</Text>
                    <br />
                    <Text type="secondary">工作时间：周一至周日 9:00-18:00</Text>
                  </div>

                  <div>
                    <Title level={5}>
                      <MailOutlined /> 邮箱支持
                    </Title>
                    <Text><EMAIL></Text>
                    <br />
                    <Text type="secondary">我们会在24小时内回复您的邮件</Text>
                  </div>

                  <div>
                    <Title level={5}>
                      <MessageOutlined /> 在线客服
                    </Title>
                    <Button type="primary">
                      启动在线客服
                    </Button>
                    <br />
                    <Text type="secondary">实时在线，即时响应</Text>
                  </div>
                </Space>
              </Card>
            </Col>

            <Col span={12}>
              <Card title="服务承诺">
                <Timeline>
                  <Timeline.Item
                    dot={<ClockCircleOutlined style={{ fontSize: '16px' }} />}
                    color="blue"
                  >
                    <Text strong>响应时间</Text>
                    <br />
                    <Text type="secondary">工单提交后2小时内响应</Text>
                  </Timeline.Item>

                  <Timeline.Item
                    dot={<MessageOutlined style={{ fontSize: '16px' }} />}
                    color="green"
                  >
                    <Text strong>处理时效</Text>
                    <br />
                    <Text type="secondary">一般问题24小时内解决</Text>
                  </Timeline.Item>

                  <Timeline.Item
                    dot={<CheckCircleOutlined style={{ fontSize: '16px' }} />}
                    color="green"
                  >
                    <Text strong>满意保证</Text>
                    <br />
                    <Text type="secondary">不满意可申请升级处理</Text>
                  </Timeline.Item>
                </Timeline>
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>

      {/* 工单提交模态框 */}
      <Modal
        title="提交工单"
        open={ticketModalVisible}
        onCancel={() => {
          setTicketModalVisible(false);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        confirmLoading={loading}
        okText="提交工单"
        cancelText="取消"
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmitTicket}
        >
          <Form.Item
            name="title"
            label="问题标题"
            rules={[{ required: true, message: '请输入问题标题' }]}
          >
            <Input placeholder="请简要描述您遇到的问题" />
          </Form.Item>

          <Form.Item
            name="category"
            label="问题类别"
            rules={[{ required: true, message: '请选择问题类别' }]}
          >
            <Select placeholder="请选择问题类别">
              <Option value="产品咨询">产品咨询</Option>
              <Option value="订单问题">订单问题</Option>
              <Option value="配送问题">配送问题</Option>
              <Option value="质量问题">质量问题</Option>
              <Option value="售后服务">售后服务</Option>
              <Option value="技术支持">技术支持</Option>
              <Option value="其他">其他</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="priority"
            label="优先级"
            rules={[{ required: true, message: '请选择优先级' }]}
          >
            <Select placeholder="请选择优先级">
              <Option value="LOW">低 - 一般咨询</Option>
              <Option value="MEDIUM">中 - 影响使用</Option>
              <Option value="HIGH">高 - 严重影响</Option>
              <Option value="URGENT">紧急 - 无法使用</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="详细描述"
            rules={[{ required: true, message: '请详细描述问题' }]}
          >
            <TextArea
              rows={4}
              placeholder="请详细描述您遇到的问题，包括具体情况、错误信息等"
              maxLength={1000}
              showCount
            />
          </Form.Item>

          <Form.Item name="projectId" label="关联项目（可选）">
            <Select placeholder="如果问题与特定项目相关，请选择" allowClear>
              <Option value={1}>王先生客厅家具定制项目</Option>
              {/* 这里可以动态加载用户的项目列表 */}
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 满意度调查模态框 */}
      <Modal
        title="满意度调查"
        open={surveyModalVisible}
        onCancel={() => {
          setSurveyModalVisible(false);
          surveyForm.resetFields();
        }}
        onOk={() => surveyForm.submit()}
        confirmLoading={loading}
        okText="提交调查"
        cancelText="取消"
        width={500}
      >
        <Form
          form={surveyForm}
          layout="vertical"
          onFinish={handleSubmitSurvey}
        >
          <Form.Item
            name="projectId"
            label="评价项目"
            rules={[{ required: true, message: '请选择要评价的项目' }]}
          >
            <Select placeholder="请选择要评价的项目">
              <Option value={1}>王先生客厅家具定制项目</Option>
              {/* 这里可以动态加载用户的项目列表 */}
            </Select>
          </Form.Item>

          <Form.Item
            name="score"
            label="整体评分"
            rules={[{ required: true, message: '请给出评分' }]}
          >
            <Rate />
          </Form.Item>

          <Form.Item
            name="feedback"
            label="详细反馈"
          >
            <TextArea
              rows={4}
              placeholder="请分享您的使用体验和建议..."
              maxLength={500}
              showCount
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Support;
