import React, { useEffect } from 'react';
import { Row, Col, Card, Statistic, Typography, List, Tag, Button, Space, Progress } from 'antd';
import {
  FileTextOutlined,
  SolutionOutlined,
  ProjectOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  RightOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAppSelector } from '@/store';
import { mockRequirements, mockProposals, mockProjects } from '@/data/mockData';
import { STATUS_COLORS } from '@/constants';

const { Title, Text } = Typography;

const CustomerDashboard: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAppSelector((state) => state.auth);

  // 模拟数据 - 实际应用中应该从API获取
  const stats = {
    totalRequirements: 3,
    activeProposals: 2,
    ongoingProjects: 1,
    completedProjects: 2,
  };

  const recentRequirements = mockRequirements.slice(0, 3);
  const recentProposals = mockProposals.slice(0, 3);
  const activeProjects = mockProjects.slice(0, 2);

  const getStatusColor = (status: string) => {
    return STATUS_COLORS[status as keyof typeof STATUS_COLORS] || '#d9d9d9';
  };

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      SUBMITTED: '已提交',
      ANALYZING: '分析中',
      ASSIGNED: '已分配',
      IN_PROGRESS: '进行中',
      COMPLETED: '已完成',
      DRAFT: '草稿',
      REVIEWING: '审核中',
      SENT: '已发送',
      CONFIRMED: '已确认',
      REJECTED: '已拒绝',
      PLANNING: '规划中',
      DELAYED: '已延期',
      CANCELLED: '已取消',
    };
    return statusMap[status] || status;
  };

  return (
    <div className="page-container">
      <div className="page-header">
        <Title level={3} className="page-title">
          欢迎回来，{user?.fullName}
        </Title>
        <Text className="page-description">
          这里是您的个人工作台，可以查看最新的项目进展和重要信息
        </Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="我的需求"
              value={stats.totalRequirements}
              prefix={<FileTextOutlined style={{ color: '#1890ff' }} />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="待确认方案"
              value={stats.activeProposals}
              prefix={<SolutionOutlined style={{ color: '#52c41a' }} />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="进行中项目"
              value={stats.ongoingProjects}
              prefix={<ProjectOutlined style={{ color: '#fa8c16' }} />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="已完成项目"
              value={stats.completedProjects}
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              suffix="个"
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 最近需求 */}
        <Col xs={24} lg={8}>
          <Card
            title="最近需求"
            extra={
              <Button
                type="link"
                icon={<RightOutlined />}
                onClick={() => navigate('/customer/requirements')}
              >
                查看全部
              </Button>
            }
          >
            <List
              dataSource={recentRequirements}
              renderItem={(item) => (
                <List.Item
                  actions={[
                    <Tag color={getStatusColor(item.status)}>
                      {getStatusText(item.status)}
                    </Tag>
                  ]}
                >
                  <List.Item.Meta
                    title={
                      <a onClick={() => navigate(`/customer/requirements/${item.id}`)}>
                        {item.title}
                      </a>
                    }
                    description={
                      <Space direction="vertical" size={4}>
                        <Text type="secondary" ellipsis>
                          {item.description}
                        </Text>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {item.createdAt.split('T')[0]}
                        </Text>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 设计方案 */}
        <Col xs={24} lg={8}>
          <Card
            title="设计方案"
            extra={
              <Button
                type="link"
                icon={<RightOutlined />}
                onClick={() => navigate('/customer/proposals')}
              >
                查看全部
              </Button>
            }
          >
            <List
              dataSource={recentProposals}
              renderItem={(item) => (
                <List.Item
                  actions={[
                    <Tag color={getStatusColor(item.status)}>
                      {getStatusText(item.status)}
                    </Tag>
                  ]}
                >
                  <List.Item.Meta
                    title={
                      <a onClick={() => navigate(`/customer/proposals/${item.id}`)}>
                        {item.title}
                      </a>
                    }
                    description={
                      <Space direction="vertical" size={4}>
                        <Text type="secondary">
                          总价：¥{item.totalCost.toLocaleString()}
                        </Text>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {item.createdAt.split('T')[0]}
                        </Text>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 项目进度 */}
        <Col xs={24} lg={8}>
          <Card
            title="项目进度"
            extra={
              <Button
                type="link"
                icon={<RightOutlined />}
                onClick={() => navigate('/customer/projects')}
              >
                查看全部
              </Button>
            }
          >
            <List
              dataSource={activeProjects}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    title={
                      <a onClick={() => navigate(`/customer/projects/${item.id}`)}>
                        {item.name}
                      </a>
                    }
                    description={
                      <Space direction="vertical" size={8} style={{ width: '100%' }}>
                        <Progress
                          percent={item.progressPercentage}
                          size="small"
                          status={item.status === 'DELAYED' ? 'exception' : 'active'}
                        />
                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            预计完成：{item.endDate}
                          </Text>
                          <Tag
                            color={getStatusColor(item.status)}
                            style={{ fontSize: 10, padding: '0 4px' }}
                          >
                            {getStatusText(item.status)}
                          </Tag>
                        </div>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Card title="快速操作" style={{ marginTop: 24 }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={6}>
            <Button
              type="primary"
              block
              icon={<FileTextOutlined />}
              onClick={() => navigate('/customer/requirements')}
            >
              提交新需求
            </Button>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Button
              block
              icon={<SolutionOutlined />}
              onClick={() => navigate('/customer/proposals')}
            >
              查看设计方案
            </Button>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Button
              block
              icon={<ProjectOutlined />}
              onClick={() => navigate('/customer/projects')}
            >
              跟踪项目进度
            </Button>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Button
              block
              icon={<ClockCircleOutlined />}
              onClick={() => navigate('/customer/support')}
            >
              联系客服
            </Button>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default CustomerDashboard;
