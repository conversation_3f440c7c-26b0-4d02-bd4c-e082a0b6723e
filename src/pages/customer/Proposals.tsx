import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Input,
  Select,
  Modal,
  Row,
  Col,
  Descriptions,
  Image,
  List,
  Avatar,
  Divider,
  Rate,
  message,
  Tooltip,
  Progress,
  Timeline,
} from 'antd';
import {
  EyeOutlined,
  CheckOutlined,
  CloseOutlined,
  CommentOutlined,
  DownloadOutlined,
  SearchOutlined,
  HeartOutlined,
  ShareAltOutlined,
  PrinterOutlined,
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import { useAppSelector } from '@/store';
import { mockProposals, mockRequirements } from '@/data/mockData';
import { STATUS_COLORS } from '@/constants';
import type { Proposal, ProposalComment } from '@/types';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const Proposals: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const [proposals, setProposals] = useState<Proposal[]>(mockProposals);
  const [loading, setLoading] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [commentModalVisible, setCommentModalVisible] = useState(false);
  const [selectedProposal, setSelectedProposal] = useState<Proposal | null>(null);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [newComment, setNewComment] = useState('');
  const [rating, setRating] = useState(0);

  const { user } = useAppSelector((state) => state.auth);

  useEffect(() => {
    if (id) {
      const proposal = proposals.find(p => p.id === parseInt(id));
      if (proposal) {
        setSelectedProposal(proposal);
        setDetailModalVisible(true);
      }
    }
  }, [id, proposals]);

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      DRAFT: '草稿',
      REVIEWING: '审核中',
      SENT: '已发送',
      CONFIRMED: '已确认',
      REJECTED: '已拒绝',
    };
    return statusMap[status] || status;
  };

  const columns: ColumnsType<Proposal> = [
    {
      title: '方案标题',
      dataIndex: 'title',
      key: 'title',
      render: (text, record) => (
        <a onClick={() => handleViewDetail(record)}>{text}</a>
      ),
    },
    {
      title: '关联需求',
      dataIndex: ['requirement', 'title'],
      key: 'requirement',
      ellipsis: true,
    },
    {
      title: '总价',
      dataIndex: 'totalCost',
      key: 'totalCost',
      render: (cost) => `¥${cost.toLocaleString()}`,
      sorter: (a, b) => a.totalCost - b.totalCost,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={STATUS_COLORS[status as keyof typeof STATUS_COLORS]}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '设计师',
      dataIndex: ['createdUser', 'fullName'],
      key: 'designer',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => date.split('T')[0],
      sorter: (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="查看详情">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          {record.status === 'SENT' && (
            <>
              <Tooltip title="确认方案">
                <Button
                  type="link"
                  icon={<CheckOutlined />}
                  style={{ color: '#52c41a' }}
                  onClick={() => handleConfirm(record)}
                />
              </Tooltip>
              <Tooltip title="拒绝方案">
                <Button
                  type="link"
                  icon={<CloseOutlined />}
                  style={{ color: '#ff4d4f' }}
                  onClick={() => handleReject(record)}
                />
              </Tooltip>
              <Tooltip title="添加评论">
                <Button
                  type="link"
                  icon={<CommentOutlined />}
                  onClick={() => handleAddComment(record)}
                />
              </Tooltip>
            </>
          )}
          <Tooltip title="下载方案">
            <Button
              type="link"
              icon={<DownloadOutlined />}
              onClick={() => handleDownload(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const handleViewDetail = (proposal: Proposal) => {
    setSelectedProposal(proposal);
    setDetailModalVisible(true);
    navigate(`/customer/proposals/${proposal.id}`);
  };

  const handleConfirm = async (proposal: Proposal) => {
    try {
      setLoading(true);
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      const updatedProposals = proposals.map(p =>
        p.id === proposal.id
          ? { ...p, status: 'CONFIRMED' as const, updatedAt: new Date().toISOString() }
          : p
      );
      setProposals(updatedProposals);
      message.success('方案确认成功！');
    } catch (error) {
      message.error('操作失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleReject = async (proposal: Proposal) => {
    try {
      setLoading(true);
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      const updatedProposals = proposals.map(p =>
        p.id === proposal.id
          ? { ...p, status: 'REJECTED' as const, updatedAt: new Date().toISOString() }
          : p
      );
      setProposals(updatedProposals);
      message.success('方案已拒绝');
    } catch (error) {
      message.error('操作失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleAddComment = (proposal: Proposal) => {
    setSelectedProposal(proposal);
    setCommentModalVisible(true);
  };

  const handleDownload = async (proposal: Proposal) => {
    try {
      message.info('正在生成PDF文件...');
      // 模拟下载
      await new Promise(resolve => setTimeout(resolve, 2000));
      message.success('方案PDF已下载');
    } catch (error) {
      message.error('下载失败，请重试');
    }
  };

  const handleSubmitComment = async () => {
    if (!newComment.trim()) {
      message.warning('请输入评论内容');
      return;
    }

    try {
      setLoading(true);
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      message.success('评论提交成功！');
      setNewComment('');
      setRating(0);
      setCommentModalVisible(false);
    } catch (error) {
      message.error('提交失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const filteredProposals = proposals.filter(proposal => {
    const matchesSearch = !searchText ||
      proposal.title.toLowerCase().includes(searchText.toLowerCase()) ||
      proposal.description.toLowerCase().includes(searchText.toLowerCase());
    const matchesStatus = !statusFilter || proposal.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="page-container">
      <div className="page-header">
        <Title level={3} className="page-title">设计方案</Title>
        <Text className="page-description">
          查看和管理您的家具设计方案，与设计师协作完善方案细节
        </Text>
      </div>

      <Card>
        <div className="table-header">
          <Space>
            <Input
              placeholder="搜索方案标题或描述"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 250 }}
            />
            <Select
              placeholder="筛选状态"
              value={statusFilter}
              onChange={setStatusFilter}
              allowClear
              style={{ width: 150 }}
            >
              <Option value="DRAFT">草稿</Option>
              <Option value="REVIEWING">审核中</Option>
              <Option value="SENT">已发送</Option>
              <Option value="CONFIRMED">已确认</Option>
              <Option value="REJECTED">已拒绝</Option>
            </Select>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={filteredProposals}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredProposals.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 方案详情模态框 */}
      <Modal
        title="方案详情"
        open={detailModalVisible}
        onCancel={() => {
          setDetailModalVisible(false);
          setSelectedProposal(null);
          navigate('/customer/proposals');
        }}
        footer={null}
        width={1200}
        destroyOnClose
      >
        {selectedProposal && (
          <div>
            <Row gutter={[24, 24]}>
              <Col span={16}>
                <Card title="方案信息" size="small">
                  <Descriptions column={2} size="small">
                    <Descriptions.Item label="方案标题">
                      {selectedProposal.title}
                    </Descriptions.Item>
                    <Descriptions.Item label="状态">
                      <Tag color={STATUS_COLORS[selectedProposal.status as keyof typeof STATUS_COLORS]}>
                        {getStatusText(selectedProposal.status)}
                      </Tag>
                    </Descriptions.Item>
                    <Descriptions.Item label="总价">
                      ¥{selectedProposal.totalCost.toLocaleString()}
                    </Descriptions.Item>
                    <Descriptions.Item label="利润率">
                      {(selectedProposal.profitMargin * 100).toFixed(1)}%
                    </Descriptions.Item>
                    <Descriptions.Item label="设计师">
                      {selectedProposal.createdUser?.fullName}
                    </Descriptions.Item>
                    <Descriptions.Item label="创建时间">
                      {selectedProposal.createdAt.split('T')[0]}
                    </Descriptions.Item>
                  </Descriptions>

                  <Divider />

                  <Title level={5}>方案描述</Title>
                  <Paragraph>{selectedProposal.description}</Paragraph>
                </Card>

                <Card title="方案组件" size="small" style={{ marginTop: 16 }}>
                  <List
                    dataSource={selectedProposal.components}
                    renderItem={(component) => (
                      <List.Item
                        actions={[
                          <Text key="cost">¥{component.totalCost.toLocaleString()}</Text>
                        ]}
                      >
                        <List.Item.Meta
                          avatar={
                            <Avatar
                              shape="square"
                              size={64}
                              src={component.imageUrl}
                              icon={<HeartOutlined />}
                            />
                          }
                          title={component.componentName}
                          description={
                            <Space direction="vertical" size={4}>
                              <Text type="secondary">
                                材料：{component.material}
                              </Text>
                              <Text type="secondary">
                                尺寸：{component.dimensions}
                              </Text>
                              <Text type="secondary">
                                数量：{component.quantity}
                              </Text>
                              {component.description && (
                                <Text type="secondary">
                                  {component.description}
                                </Text>
                              )}
                            </Space>
                          }
                        />
                      </List.Item>
                    )}
                  />
                </Card>
              </Col>

              <Col span={8}>
                <Card title="操作" size="small">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    {selectedProposal.status === 'SENT' && (
                      <>
                        <Button
                          type="primary"
                          icon={<CheckOutlined />}
                          block
                          onClick={() => handleConfirm(selectedProposal)}
                          loading={loading}
                        >
                          确认方案
                        </Button>
                        <Button
                          danger
                          icon={<CloseOutlined />}
                          block
                          onClick={() => handleReject(selectedProposal)}
                          loading={loading}
                        >
                          拒绝方案
                        </Button>
                        <Button
                          icon={<CommentOutlined />}
                          block
                          onClick={() => handleAddComment(selectedProposal)}
                        >
                          添加评论
                        </Button>
                      </>
                    )}
                    <Button
                      icon={<DownloadOutlined />}
                      block
                      onClick={() => handleDownload(selectedProposal)}
                    >
                      下载PDF
                    </Button>
                    <Button
                      icon={<PrinterOutlined />}
                      block
                      onClick={() => window.print()}
                    >
                      打印方案
                    </Button>
                    <Button
                      icon={<ShareAltOutlined />}
                      block
                      onClick={() => {
                        navigator.clipboard.writeText(window.location.href);
                        message.success('链接已复制到剪贴板');
                      }}
                    >
                      分享方案
                    </Button>
                  </Space>
                </Card>

                <Card title="方案进度" size="small" style={{ marginTop: 16 }}>
                  <Timeline
                    size="small"
                    items={[
                      {
                        color: 'green',
                        children: '需求分析完成',
                      },
                      {
                        color: 'green',
                        children: '初步方案设计',
                      },
                      {
                        color: selectedProposal.status === 'SENT' ? 'blue' : 'green',
                        children: '方案发送给客户',
                      },
                      {
                        color: selectedProposal.status === 'CONFIRMED' ? 'green' : 'gray',
                        children: '客户确认方案',
                      },
                      {
                        color: 'gray',
                        children: '进入项目实施',
                      },
                    ]}
                  />
                </Card>

                {selectedProposal.comments && selectedProposal.comments.length > 0 && (
                  <Card title="评论记录" size="small" style={{ marginTop: 16 }}>
                    <List
                      size="small"
                      dataSource={selectedProposal.comments}
                      renderItem={(comment) => (
                        <List.Item>
                          <List.Item.Meta
                            avatar={<Avatar size="small">{comment.user?.fullName?.[0]}</Avatar>}
                            title={
                              <Space>
                                <Text strong>{comment.user?.fullName}</Text>
                                <Tag size="small" color={comment.userType === 'CUSTOMER' ? 'blue' : 'green'}>
                                  {comment.userType === 'CUSTOMER' ? '客户' : '内部'}
                                </Tag>
                              </Space>
                            }
                            description={
                              <Space direction="vertical" size={4}>
                                <Text>{comment.content}</Text>
                                <Text type="secondary" style={{ fontSize: 12 }}>
                                  {comment.createdAt.split('T')[0]}
                                </Text>
                              </Space>
                            }
                          />
                        </List.Item>
                      )}
                    />
                  </Card>
                )}
              </Col>
            </Row>
          </div>
        )}
      </Modal>

      {/* 评论模态框 */}
      <Modal
        title="添加评论"
        open={commentModalVisible}
        onCancel={() => {
          setCommentModalVisible(false);
          setNewComment('');
          setRating(0);
        }}
        onOk={handleSubmitComment}
        confirmLoading={loading}
        okText="提交评论"
        cancelText="取消"
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>方案评分：</Text>
            <Rate value={rating} onChange={setRating} />
          </div>
          <div>
            <Text strong>评论内容：</Text>
            <TextArea
              rows={4}
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder="请输入您对方案的意见和建议..."
              maxLength={500}
              showCount
            />
          </div>
        </Space>
      </Modal>
    </div>
  );
};

export default Proposals;
