import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Input,
  Select,
  Modal,
  Row,
  Col,
  Progress,
  Timeline,
  Descriptions,
  List,
  Avatar,
  Divider,
  Tabs,
  Alert,
  Statistic,
  Calendar,
  Badge,
  message,
  Tooltip,
} from 'antd';
import {
  EyeOutlined,
  CalendarOutlined,
  TeamOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  SearchOutlined,
  DownloadOutlined,
  MessageOutlined,
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import { useAppSelector } from '@/store';
import { mockProjects, mockUsers } from '@/data/mockData';
import { STATUS_COLORS } from '@/constants';
import type { Project, ProjectMilestone, ProjectTask } from '@/types';
import type { ColumnsType } from 'antd/es/table';
import type { Moment } from 'moment';
import dayjs from 'dayjs';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

const Projects: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const [projects, setProjects] = useState<Project[]>(mockProjects);
  const [loading, setLoading] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [activeTab, setActiveTab] = useState('overview');

  const { user } = useAppSelector((state) => state.auth);

  useEffect(() => {
    if (id) {
      const project = projects.find(p => p.id === parseInt(id));
      if (project) {
        setSelectedProject(project);
        setDetailModalVisible(true);
      }
    }
  }, [id, projects]);

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      PLANNING: '规划中',
      IN_PROGRESS: '进行中',
      DELAYED: '已延期',
      COMPLETED: '已完成',
      CANCELLED: '已取消',
    };
    return statusMap[status] || status;
  };

  const getStatusIcon = (status: string) => {
    const iconMap: Record<string, React.ReactNode> = {
      PLANNING: <ClockCircleOutlined />,
      IN_PROGRESS: <ExclamationCircleOutlined />,
      DELAYED: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />,
      COMPLETED: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      CANCELLED: <CloseCircleOutlined style={{ color: '#8c8c8c' }} />,
    };
    return iconMap[status] || <ClockCircleOutlined />;
  };

  const columns: ColumnsType<Project> = [
    {
      title: '项目名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <a onClick={() => handleViewDetail(record)}>{text}</a>
      ),
    },
    {
      title: '进度',
      dataIndex: 'progressPercentage',
      key: 'progress',
      render: (progress, record) => (
        <Space direction="vertical" size={4} style={{ width: '100%' }}>
          <Progress
            percent={progress}
            size="small"
            status={record.status === 'DELAYED' ? 'exception' : 'active'}
          />
          <Text type="secondary" style={{ fontSize: 12 }}>
            {progress}% 完成
          </Text>
        </Space>
      ),
      width: 150,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag
          icon={getStatusIcon(status)}
          color={STATUS_COLORS[status as keyof typeof STATUS_COLORS]}
        >
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '项目经理',
      dataIndex: ['projectManager', 'fullName'],
      key: 'manager',
    },
    {
      title: '开始时间',
      dataIndex: 'startDate',
      key: 'startDate',
      sorter: (a, b) => new Date(a.startDate).getTime() - new Date(b.startDate).getTime(),
    },
    {
      title: '预计完成',
      dataIndex: 'endDate',
      key: 'endDate',
      render: (date, record) => {
        const isOverdue = new Date(date) < new Date() && record.status !== 'COMPLETED';
        return (
          <Text type={isOverdue ? 'danger' : 'secondary'}>
            {date}
          </Text>
        );
      },
      sorter: (a, b) => new Date(a.endDate).getTime() - new Date(b.endDate).getTime(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="查看详情">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          <Tooltip title="项目日历">
            <Button
              type="link"
              icon={<CalendarOutlined />}
              onClick={() => handleViewCalendar(record)}
            />
          </Tooltip>
          <Tooltip title="团队沟通">
            <Button
              type="link"
              icon={<MessageOutlined />}
              onClick={() => handleTeamChat(record)}
            />
          </Tooltip>
          <Tooltip title="下载报告">
            <Button
              type="link"
              icon={<DownloadOutlined />}
              onClick={() => handleDownloadReport(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const handleViewDetail = (project: Project) => {
    setSelectedProject(project);
    setDetailModalVisible(true);
    navigate(`/customer/projects/${project.id}`);
  };

  const handleViewCalendar = (project: Project) => {
    setSelectedProject(project);
    setActiveTab('calendar');
    setDetailModalVisible(true);
  };

  const handleTeamChat = (project: Project) => {
    message.info('团队沟通功能开发中...');
  };

  const handleDownloadReport = async (project: Project) => {
    try {
      message.info('正在生成项目报告...');
      await new Promise(resolve => setTimeout(resolve, 2000));
      message.success('项目报告已下载');
    } catch (error) {
      message.error('下载失败，请重试');
    }
  };

  const filteredProjects = projects.filter(project => {
    const matchesSearch = !searchText ||
      project.name.toLowerCase().includes(searchText.toLowerCase()) ||
      project.description.toLowerCase().includes(searchText.toLowerCase());
    const matchesStatus = !statusFilter || project.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getListData = (value: Moment) => {
    if (!selectedProject) return [];

    const dateStr = value.format('YYYY-MM-DD');
    const milestones = selectedProject.milestones.filter(m =>
      m.plannedDate === dateStr || m.actualDate === dateStr
    );

    return milestones.map(milestone => ({
      type: milestone.status === 'COMPLETED' ? 'success' :
            milestone.status === 'DELAYED' ? 'error' : 'warning',
      content: milestone.name,
    }));
  };

  const dateCellRender = (value: Moment) => {
    const listData = getListData(value);
    return (
      <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
        {listData.map((item, index) => (
          <li key={index}>
            <Badge status={item.type as any} text={item.content} />
          </li>
        ))}
      </ul>
    );
  };

  return (
    <div className="page-container">
      <div className="page-header">
        <Title level={3} className="page-title">项目进度</Title>
        <Text className="page-description">
          实时跟踪您的家具定制项目进展，了解每个阶段的完成情况
        </Text>
      </div>

      <Card>
        <div className="table-header">
          <Space>
            <Input
              placeholder="搜索项目名称或描述"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 250 }}
            />
            <Select
              placeholder="筛选状态"
              value={statusFilter}
              onChange={setStatusFilter}
              allowClear
              style={{ width: 150 }}
            >
              <Option value="PLANNING">规划中</Option>
              <Option value="IN_PROGRESS">进行中</Option>
              <Option value="DELAYED">已延期</Option>
              <Option value="COMPLETED">已完成</Option>
              <Option value="CANCELLED">已取消</Option>
            </Select>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={filteredProjects}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredProjects.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 项目详情模态框 */}
      <Modal
        title="项目详情"
        open={detailModalVisible}
        onCancel={() => {
          setDetailModalVisible(false);
          setSelectedProject(null);
          setActiveTab('overview');
          navigate('/customer/projects');
        }}
        footer={null}
        width={1200}
        destroyOnClose
      >
        {selectedProject && (
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab="项目概览" key="overview">
              <Row gutter={[24, 24]}>
                <Col span={16}>
                  <Card title="项目信息" size="small">
                    <Descriptions column={2} size="small">
                      <Descriptions.Item label="项目名称">
                        {selectedProject.name}
                      </Descriptions.Item>
                      <Descriptions.Item label="状态">
                        <Tag
                          icon={getStatusIcon(selectedProject.status)}
                          color={STATUS_COLORS[selectedProject.status as keyof typeof STATUS_COLORS]}
                        >
                          {getStatusText(selectedProject.status)}
                        </Tag>
                      </Descriptions.Item>
                      <Descriptions.Item label="项目经理">
                        {selectedProject.projectManager?.fullName}
                      </Descriptions.Item>
                      <Descriptions.Item label="进度">
                        <Progress
                          percent={selectedProject.progressPercentage}
                          size="small"
                          status={selectedProject.status === 'DELAYED' ? 'exception' : 'active'}
                        />
                      </Descriptions.Item>
                      <Descriptions.Item label="开始时间">
                        {selectedProject.startDate}
                      </Descriptions.Item>
                      <Descriptions.Item label="预计完成">
                        {selectedProject.endDate}
                      </Descriptions.Item>
                      {selectedProject.actualStartDate && (
                        <Descriptions.Item label="实际开始">
                          {selectedProject.actualStartDate}
                        </Descriptions.Item>
                      )}
                      {selectedProject.actualEndDate && (
                        <Descriptions.Item label="实际完成">
                          {selectedProject.actualEndDate}
                        </Descriptions.Item>
                      )}
                    </Descriptions>

                    <Divider />

                    <Title level={5}>项目描述</Title>
                    <Paragraph>{selectedProject.description}</Paragraph>
                  </Card>

                  <Card title="里程碑进度" size="small" style={{ marginTop: 16 }}>
                    <Timeline>
                      {selectedProject.milestones.map((milestone) => (
                        <Timeline.Item
                          key={milestone.id}
                          color={
                            milestone.status === 'COMPLETED' ? 'green' :
                            milestone.status === 'IN_PROGRESS' ? 'blue' :
                            milestone.status === 'DELAYED' ? 'red' : 'gray'
                          }
                          dot={
                            milestone.status === 'COMPLETED' ? <CheckCircleOutlined /> :
                            milestone.status === 'IN_PROGRESS' ? <ClockCircleOutlined /> :
                            milestone.status === 'DELAYED' ? <ExclamationCircleOutlined /> :
                            <ClockCircleOutlined />
                          }
                        >
                          <div>
                            <Text strong>{milestone.name}</Text>
                            <br />
                            <Text type="secondary">{milestone.description}</Text>
                            <br />
                            <Space>
                              <Text type="secondary">
                                计划：{milestone.plannedDate}
                              </Text>
                              {milestone.actualDate && (
                                <Text type="secondary">
                                  实际：{milestone.actualDate}
                                </Text>
                              )}
                              <Tag
                                size="small"
                                color={STATUS_COLORS[milestone.status as keyof typeof STATUS_COLORS]}
                              >
                                {getStatusText(milestone.status)}
                              </Tag>
                            </Space>
                          </div>
                        </Timeline.Item>
                      ))}
                    </Timeline>
                  </Card>
                </Col>

                <Col span={8}>
                  <Card title="项目统计" size="small">
                    <Row gutter={[16, 16]}>
                      <Col span={12}>
                        <Statistic
                          title="总里程碑"
                          value={selectedProject.milestones.length}
                          suffix="个"
                        />
                      </Col>
                      <Col span={12}>
                        <Statistic
                          title="已完成"
                          value={selectedProject.milestones.filter(m => m.status === 'COMPLETED').length}
                          suffix="个"
                        />
                      </Col>
                      <Col span={12}>
                        <Statistic
                          title="进行中"
                          value={selectedProject.milestones.filter(m => m.status === 'IN_PROGRESS').length}
                          suffix="个"
                        />
                      </Col>
                      <Col span={12}>
                        <Statistic
                          title="延期"
                          value={selectedProject.milestones.filter(m => m.status === 'DELAYED').length}
                          suffix="个"
                          valueStyle={{ color: '#ff4d4f' }}
                        />
                      </Col>
                    </Row>
                  </Card>

                  {selectedProject.status === 'DELAYED' && (
                    <Alert
                      message="项目延期提醒"
                      description="项目进度有所延期，请及时与项目经理沟通了解具体情况。"
                      type="warning"
                      showIcon
                      style={{ marginTop: 16 }}
                    />
                  )}

                  <Card title="快速操作" size="small" style={{ marginTop: 16 }}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Button
                        icon={<MessageOutlined />}
                        block
                        onClick={() => handleTeamChat(selectedProject)}
                      >
                        联系项目经理
                      </Button>
                      <Button
                        icon={<CalendarOutlined />}
                        block
                        onClick={() => setActiveTab('calendar')}
                      >
                        查看项目日历
                      </Button>
                      <Button
                        icon={<DownloadOutlined />}
                        block
                        onClick={() => handleDownloadReport(selectedProject)}
                      >
                        下载进度报告
                      </Button>
                    </Space>
                  </Card>
                </Col>
              </Row>
            </TabPane>

            <TabPane tab="项目日历" key="calendar">
              <Card>
                <Calendar
                  dateCellRender={dateCellRender}
                  headerRender={({ value, type, onChange, onTypeChange }) => (
                    <div style={{ padding: 8 }}>
                      <Row justify="space-between" align="middle">
                        <Col>
                          <Title level={4} style={{ margin: 0 }}>
                            {value.format('YYYY年MM月')} 项目日历
                          </Title>
                        </Col>
                        <Col>
                          <Space>
                            <Button
                              size="small"
                              onClick={() => onChange(value.clone().subtract(1, 'month'))}
                            >
                              上月
                            </Button>
                            <Button
                              size="small"
                              onClick={() => onChange(dayjs())}
                            >
                              今天
                            </Button>
                            <Button
                              size="small"
                              onClick={() => onChange(value.clone().add(1, 'month'))}
                            >
                              下月
                            </Button>
                          </Space>
                        </Col>
                      </Row>
                    </div>
                  )}
                />
              </Card>
            </TabPane>

            <TabPane tab="团队信息" key="team">
              <Card title="项目团队">
                <List
                  dataSource={[
                    {
                      id: selectedProject.projectManagerId,
                      name: selectedProject.projectManager?.fullName || '项目经理',
                      role: '项目经理',
                      avatar: selectedProject.projectManager?.avatar,
                      email: selectedProject.projectManager?.email,
                      phone: selectedProject.projectManager?.phone,
                    },
                    // 可以添加更多团队成员
                  ]}
                  renderItem={(member) => (
                    <List.Item
                      actions={[
                        <Button type="link" icon={<MessageOutlined />}>
                          联系
                        </Button>
                      ]}
                    >
                      <List.Item.Meta
                        avatar={<Avatar src={member.avatar}>{member.name[0]}</Avatar>}
                        title={member.name}
                        description={
                          <Space direction="vertical" size={4}>
                            <Text type="secondary">{member.role}</Text>
                            {member.email && (
                              <Text type="secondary">{member.email}</Text>
                            )}
                            {member.phone && (
                              <Text type="secondary">{member.phone}</Text>
                            )}
                          </Space>
                        }
                      />
                    </List.Item>
                  )}
                />
              </Card>
            </TabPane>
          </Tabs>
        )}
      </Modal>
    </div>
  );
};

export default Projects;
