import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Input,
  Select,
  Modal,
  Row,
  Col,
  Descriptions,
  List,
  Avatar,
  Rate,
  Upload,
  Form,
  Divider,
  Steps,
  Alert,
  Image,
  message,
  Tooltip,
  Progress,
  Timeline,
} from 'antd';
import {
  EyeOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  DownloadOutlined,
  SearchOutlined,
  FileImageOutlined,
  FilePdfOutlined,
  FileTextOutlined,
  StarOutlined,
  MessageOutlined,
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import { useAppSelector } from '@/store';
import { mockDeliveries } from '@/data/mockData';
import { STATUS_COLORS } from '@/constants';
import type { Delivery, AcceptanceRecord, AcceptanceIssue } from '@/types';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;
const { Step } = Steps;

const Deliveries: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [form] = Form.useForm();

  const [deliveries, setDeliveries] = useState<Delivery[]>(mockDeliveries);
  const [loading, setLoading] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [acceptanceModalVisible, setAcceptanceModalVisible] = useState(false);
  const [selectedDelivery, setSelectedDelivery] = useState<Delivery | null>(null);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [acceptanceData, setAcceptanceData] = useState({
    overallRating: 0,
    feedback: '',
    issues: [] as AcceptanceIssue[],
  });

  const { user } = useAppSelector((state) => state.auth);

  useEffect(() => {
    if (id) {
      const delivery = deliveries.find(d => d.id === parseInt(id));
      if (delivery) {
        setSelectedDelivery(delivery);
        setDetailModalVisible(true);
      }
    }
  }, [id, deliveries]);

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      PREPARING: '准备中',
      READY: '待交付',
      DELIVERED: '已交付',
      ACCEPTED: '已验收',
      REJECTED: '已拒绝',
    };
    return statusMap[status] || status;
  };

  const getFileIcon = (fileName: string) => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(ext || '')) {
      return <FileImageOutlined style={{ color: '#52c41a' }} />;
    } else if (ext === 'pdf') {
      return <FilePdfOutlined style={{ color: '#ff4d4f' }} />;
    } else {
      return <FileTextOutlined style={{ color: '#1890ff' }} />;
    }
  };

  const columns: ColumnsType<Delivery> = [
    {
      title: '交付编号',
      dataIndex: 'id',
      key: 'id',
      render: (id, record) => (
        <a onClick={() => handleViewDetail(record)}>
          DEL-{String(id).padStart(4, '0')}
        </a>
      ),
    },
    {
      title: '关联项目',
      dataIndex: ['project', 'name'],
      key: 'project',
      ellipsis: true,
    },
    {
      title: '交付物数量',
      dataIndex: 'items',
      key: 'itemCount',
      render: (items) => `${items.length} 项`,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={STATUS_COLORS[status as keyof typeof STATUS_COLORS]}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '交付日期',
      dataIndex: 'deliveryDate',
      key: 'deliveryDate',
      sorter: (a, b) => new Date(a.deliveryDate).getTime() - new Date(b.deliveryDate).getTime(),
    },
    {
      title: '交付人',
      dataIndex: ['deliveredUser', 'fullName'],
      key: 'deliveredBy',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="查看详情">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          {record.status === 'DELIVERED' && (
            <>
              <Tooltip title="开始验收">
                <Button
                  type="link"
                  icon={<CheckOutlined />}
                  style={{ color: '#52c41a' }}
                  onClick={() => handleStartAcceptance(record)}
                />
              </Tooltip>
              <Tooltip title="拒绝交付">
                <Button
                  type="link"
                  icon={<CloseOutlined />}
                  style={{ color: '#ff4d4f' }}
                  onClick={() => handleRejectDelivery(record)}
                />
              </Tooltip>
            </>
          )}
          <Tooltip title="下载清单">
            <Button
              type="link"
              icon={<DownloadOutlined />}
              onClick={() => handleDownloadList(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const handleViewDetail = (delivery: Delivery) => {
    setSelectedDelivery(delivery);
    setDetailModalVisible(true);
    navigate(`/customer/deliveries/${delivery.id}`);
  };

  const handleStartAcceptance = (delivery: Delivery) => {
    setSelectedDelivery(delivery);
    setAcceptanceModalVisible(true);
    form.resetFields();
    setAcceptanceData({
      overallRating: 0,
      feedback: '',
      issues: [],
    });
  };

  const handleRejectDelivery = async (delivery: Delivery) => {
    Modal.confirm({
      title: '确认拒绝交付',
      content: '您确定要拒绝此次交付吗？拒绝后需要重新安排交付。',
      onOk: async () => {
        try {
          setLoading(true);
          await new Promise(resolve => setTimeout(resolve, 1000));

          const updatedDeliveries = deliveries.map(d =>
            d.id === delivery.id
              ? { ...d, status: 'REJECTED' as const, updatedAt: new Date().toISOString() }
              : d
          );
          setDeliveries(updatedDeliveries);
          message.success('交付已拒绝');
        } catch (error) {
          message.error('操作失败，请重试');
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const handleDownloadList = async (delivery: Delivery) => {
    try {
      message.info('正在生成交付清单...');
      await new Promise(resolve => setTimeout(resolve, 2000));
      message.success('交付清单已下载');
    } catch (error) {
      message.error('下载失败，请重试');
    }
  };

  const handleSubmitAcceptance = async (values: any) => {
    try {
      setLoading(true);

      const acceptanceRecord: AcceptanceRecord = {
        id: Date.now(),
        deliveryId: selectedDelivery!.id,
        customerId: user!.id,
        acceptanceDate: new Date().toISOString(),
        status: acceptanceData.issues.length > 0 ? 'PARTIAL' : 'ACCEPTED',
        overallRating: acceptanceData.overallRating,
        feedback: acceptanceData.feedback,
        issuesFound: acceptanceData.issues.length,
        issues: acceptanceData.issues,
        createdAt: new Date().toISOString(),
      };

      const updatedDeliveries = deliveries.map(d =>
        d.id === selectedDelivery!.id
          ? {
              ...d,
              status: acceptanceRecord.status === 'ACCEPTED' ? 'ACCEPTED' : 'REJECTED',
              acceptanceRecord,
              updatedAt: new Date().toISOString()
            }
          : d
      );

      setDeliveries(updatedDeliveries);
      setAcceptanceModalVisible(false);
      message.success('验收完成！');
    } catch (error) {
      message.error('提交失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const addIssue = () => {
    const newIssue: AcceptanceIssue = {
      id: Date.now(),
      acceptanceRecordId: 0,
      itemId: 0,
      description: '',
      severity: 'MEDIUM',
      status: 'OPEN',
    };
    setAcceptanceData(prev => ({
      ...prev,
      issues: [...prev.issues, newIssue],
    }));
  };

  const removeIssue = (index: number) => {
    setAcceptanceData(prev => ({
      ...prev,
      issues: prev.issues.filter((_, i) => i !== index),
    }));
  };

  const updateIssue = (index: number, field: string, value: any) => {
    setAcceptanceData(prev => ({
      ...prev,
      issues: prev.issues.map((issue, i) =>
        i === index ? { ...issue, [field]: value } : issue
      ),
    }));
  };

  const filteredDeliveries = deliveries.filter(delivery => {
    const matchesSearch = !searchText ||
      delivery.project?.name.toLowerCase().includes(searchText.toLowerCase()) ||
      delivery.notes?.toLowerCase().includes(searchText.toLowerCase());
    const matchesStatus = !statusFilter || delivery.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="page-container">
      <div className="page-header">
        <Title level={3} className="page-title">交付验收</Title>
        <Text className="page-description">
          查看和验收您的家具定制项目交付成果
        </Text>
      </div>

      <Card>
        <div className="table-header">
          <Space>
            <Input
              placeholder="搜索项目名称或备注"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 250 }}
            />
            <Select
              placeholder="筛选状态"
              value={statusFilter}
              onChange={setStatusFilter}
              allowClear
              style={{ width: 150 }}
            >
              <Option value="PREPARING">准备中</Option>
              <Option value="READY">待交付</Option>
              <Option value="DELIVERED">已交付</Option>
              <Option value="ACCEPTED">已验收</Option>
              <Option value="REJECTED">已拒绝</Option>
            </Select>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={filteredDeliveries}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredDeliveries.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 交付详情模态框 */}
      <Modal
        title="交付详情"
        open={detailModalVisible}
        onCancel={() => {
          setDetailModalVisible(false);
          setSelectedDelivery(null);
          navigate('/customer/deliveries');
        }}
        footer={null}
        width={1000}
        destroyOnClose
      >
        {selectedDelivery && (
          <div>
            <Row gutter={[24, 24]}>
              <Col span={16}>
                <Card title="交付信息" size="small">
                  <Descriptions column={2} size="small">
                    <Descriptions.Item label="交付编号">
                      DEL-{String(selectedDelivery.id).padStart(4, '0')}
                    </Descriptions.Item>
                    <Descriptions.Item label="状态">
                      <Tag color={STATUS_COLORS[selectedDelivery.status as keyof typeof STATUS_COLORS]}>
                        {getStatusText(selectedDelivery.status)}
                      </Tag>
                    </Descriptions.Item>
                    <Descriptions.Item label="关联项目">
                      {selectedDelivery.project?.name}
                    </Descriptions.Item>
                    <Descriptions.Item label="交付日期">
                      {selectedDelivery.deliveryDate}
                    </Descriptions.Item>
                    <Descriptions.Item label="交付人">
                      {selectedDelivery.deliveredUser?.fullName}
                    </Descriptions.Item>
                    <Descriptions.Item label="交付物数量">
                      {selectedDelivery.items.length} 项
                    </Descriptions.Item>
                  </Descriptions>

                  {selectedDelivery.notes && (
                    <>
                      <Divider />
                      <Title level={5}>交付说明</Title>
                      <Paragraph>{selectedDelivery.notes}</Paragraph>
                    </>
                  )}
                </Card>

                <Card title="交付物清单" size="small" style={{ marginTop: 16 }}>
                  <List
                    dataSource={selectedDelivery.items}
                    renderItem={(item) => (
                      <List.Item
                        actions={[
                          <Tag
                            key="status"
                            color={STATUS_COLORS[item.status as keyof typeof STATUS_COLORS]}
                          >
                            {getStatusText(item.status)}
                          </Tag>,
                          <Tag
                            key="quality"
                            color={item.qualityCheckStatus === 'PASSED' ? 'green' :
                                   item.qualityCheckStatus === 'FAILED' ? 'red' : 'orange'}
                          >
                            {item.qualityCheckStatus === 'PASSED' ? '质检通过' :
                             item.qualityCheckStatus === 'FAILED' ? '质检失败' : '待质检'}
                          </Tag>
                        ]}
                      >
                        <List.Item.Meta
                          avatar={
                            <Avatar
                              shape="square"
                              size={64}
                              src={item.imageUrl}
                              icon={getFileIcon(item.itemName)}
                            />
                          }
                          title={item.itemName}
                          description={
                            <Space direction="vertical" size={4}>
                              <Text type="secondary">
                                类型：{item.itemType}
                              </Text>
                              {item.description && (
                                <Text type="secondary">
                                  {item.description}
                                </Text>
                              )}
                              {item.fileSize && (
                                <Text type="secondary">
                                  文件大小：{(item.fileSize / 1024 / 1024).toFixed(2)} MB
                                </Text>
                              )}
                            </Space>
                          }
                        />
                      </List.Item>
                    )}
                  />
                </Card>

                {selectedDelivery.acceptanceRecord && (
                  <Card title="验收记录" size="small" style={{ marginTop: 16 }}>
                    <Descriptions column={2} size="small">
                      <Descriptions.Item label="验收状态">
                        <Tag color={
                          selectedDelivery.acceptanceRecord.status === 'ACCEPTED' ? 'green' :
                          selectedDelivery.acceptanceRecord.status === 'REJECTED' ? 'red' : 'orange'
                        }>
                          {selectedDelivery.acceptanceRecord.status === 'ACCEPTED' ? '已接受' :
                           selectedDelivery.acceptanceRecord.status === 'REJECTED' ? '已拒绝' : '部分接受'}
                        </Tag>
                      </Descriptions.Item>
                      <Descriptions.Item label="验收时间">
                        {selectedDelivery.acceptanceRecord.acceptanceDate?.split('T')[0]}
                      </Descriptions.Item>
                      <Descriptions.Item label="整体评分">
                        <Rate disabled value={selectedDelivery.acceptanceRecord.overallRating} />
                      </Descriptions.Item>
                      <Descriptions.Item label="发现问题">
                        {selectedDelivery.acceptanceRecord.issuesFound} 个
                      </Descriptions.Item>
                    </Descriptions>

                    {selectedDelivery.acceptanceRecord.feedback && (
                      <>
                        <Divider />
                        <Title level={5}>验收反馈</Title>
                        <Paragraph>{selectedDelivery.acceptanceRecord.feedback}</Paragraph>
                      </>
                    )}

                    {selectedDelivery.acceptanceRecord.issues.length > 0 && (
                      <>
                        <Divider />
                        <Title level={5}>问题清单</Title>
                        <List
                          size="small"
                          dataSource={selectedDelivery.acceptanceRecord.issues}
                          renderItem={(issue) => (
                            <List.Item>
                              <List.Item.Meta
                                title={
                                  <Space>
                                    <Text>{issue.description}</Text>
                                    <Tag color={
                                      issue.severity === 'HIGH' ? 'red' :
                                      issue.severity === 'MEDIUM' ? 'orange' : 'blue'
                                    }>
                                      {issue.severity === 'HIGH' ? '高' :
                                       issue.severity === 'MEDIUM' ? '中' : '低'}
                                    </Tag>
                                    <Tag color={issue.status === 'RESOLVED' ? 'green' : 'red'}>
                                      {issue.status === 'RESOLVED' ? '已解决' : '待解决'}
                                    </Tag>
                                  </Space>
                                }
                                description={issue.resolution}
                              />
                            </List.Item>
                          )}
                        />
                      </>
                    )}
                  </Card>
                )}
              </Col>

              <Col span={8}>
                <Card title="交付进度" size="small">
                  <Steps
                    direction="vertical"
                    size="small"
                    current={
                      selectedDelivery.status === 'PREPARING' ? 0 :
                      selectedDelivery.status === 'READY' ? 1 :
                      selectedDelivery.status === 'DELIVERED' ? 2 :
                      selectedDelivery.status === 'ACCEPTED' ? 3 : 2
                    }
                    status={selectedDelivery.status === 'REJECTED' ? 'error' : 'process'}
                  >
                    <Step title="准备交付物" description="整理和打包交付物" />
                    <Step title="质量检查" description="内部质量检查" />
                    <Step title="交付给客户" description="正式交付" />
                    <Step title="客户验收" description="客户确认验收" />
                  </Steps>
                </Card>

                {selectedDelivery.status === 'DELIVERED' && (
                  <Card title="快速操作" size="small" style={{ marginTop: 16 }}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Button
                        type="primary"
                        icon={<CheckOutlined />}
                        block
                        onClick={() => handleStartAcceptance(selectedDelivery)}
                      >
                        开始验收
                      </Button>
                      <Button
                        danger
                        icon={<CloseOutlined />}
                        block
                        onClick={() => handleRejectDelivery(selectedDelivery)}
                      >
                        拒绝交付
                      </Button>
                      <Button
                        icon={<DownloadOutlined />}
                        block
                        onClick={() => handleDownloadList(selectedDelivery)}
                      >
                        下载清单
                      </Button>
                    </Space>
                  </Card>
                )}
              </Col>
            </Row>
          </div>
        )}
      </Modal>

      {/* 验收模态框 */}
      <Modal
        title="交付验收"
        open={acceptanceModalVisible}
        onCancel={() => {
          setAcceptanceModalVisible(false);
          setAcceptanceData({ overallRating: 0, feedback: '', issues: [] });
        }}
        onOk={() => form.submit()}
        confirmLoading={loading}
        okText="提交验收"
        cancelText="取消"
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmitAcceptance}
        >
          <Alert
            message="验收说明"
            description="请仔细检查所有交付物，如发现问题请在下方记录。验收完成后将无法修改。"
            type="info"
            showIcon
            style={{ marginBottom: 24 }}
          />

          <Form.Item label="整体评分" required>
            <Rate
              value={acceptanceData.overallRating}
              onChange={(value) => setAcceptanceData(prev => ({ ...prev, overallRating: value }))}
            />
            <Text type="secondary" style={{ marginLeft: 8 }}>
              {acceptanceData.overallRating === 0 ? '请评分' :
               acceptanceData.overallRating <= 2 ? '不满意' :
               acceptanceData.overallRating <= 3 ? '一般' :
               acceptanceData.overallRating <= 4 ? '满意' : '非常满意'}
            </Text>
          </Form.Item>

          <Form.Item label="验收反馈">
            <TextArea
              rows={4}
              value={acceptanceData.feedback}
              onChange={(e) => setAcceptanceData(prev => ({ ...prev, feedback: e.target.value }))}
              placeholder="请输入您对此次交付的整体评价和建议..."
              maxLength={500}
              showCount
            />
          </Form.Item>

          <Form.Item label="问题记录">
            <Space direction="vertical" style={{ width: '100%' }}>
              {acceptanceData.issues.map((issue, index) => (
                <Card key={index} size="small" style={{ background: '#fafafa' }}>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Input
                        placeholder="问题描述"
                        value={issue.description}
                        onChange={(e) => updateIssue(index, 'description', e.target.value)}
                      />
                    </Col>
                    <Col span={6}>
                      <Select
                        placeholder="严重程度"
                        value={issue.severity}
                        onChange={(value) => updateIssue(index, 'severity', value)}
                        style={{ width: '100%' }}
                      >
                        <Option value="LOW">轻微</Option>
                        <Option value="MEDIUM">一般</Option>
                        <Option value="HIGH">严重</Option>
                      </Select>
                    </Col>
                    <Col span={4}>
                      <Upload
                        beforeUpload={() => false}
                        showUploadList={false}
                        onChange={(info) => {
                          // 处理图片上传
                          message.info('图片上传功能开发中...');
                        }}
                      >
                        <Button icon={<UploadOutlined />} size="small">
                          上传图片
                        </Button>
                      </Upload>
                    </Col>
                    <Col span={2}>
                      <Button
                        type="link"
                        danger
                        icon={<CloseOutlined />}
                        onClick={() => removeIssue(index)}
                      />
                    </Col>
                  </Row>
                </Card>
              ))}

              <Button
                type="dashed"
                icon={<MessageOutlined />}
                onClick={addIssue}
                style={{ width: '100%' }}
              >
                添加问题记录
              </Button>
            </Space>
          </Form.Item>

          {acceptanceData.issues.length > 0 && (
            <Alert
              message="检测到问题"
              description={`您记录了 ${acceptanceData.issues.length} 个问题，提交后状态将标记为"部分接受"，需要供应商处理这些问题。`}
              type="warning"
              showIcon
            />
          )}
        </Form>
      </Modal>
    </div>
  );
};

export default Deliveries;
