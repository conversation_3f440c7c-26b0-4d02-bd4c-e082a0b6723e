import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Upload,
  Avatar,
  Row,
  Col,
  Typography,
  Divider,
  Switch,
  Select,
  DatePicker,
  message,
  Tabs,
  List,
  Tag,
  Space,
  Modal,
  Alert,
} from 'antd';
import {
  UserOutlined,
  UploadOutlined,
  EditOutlined,
  LockOutlined,
  BellOutlined,
  HistoryOutlined,
  SecurityScanOutlined,
  PhoneOutlined,
  MailOutlined,
  HomeOutlined,
} from '@ant-design/icons';
import { useAppSelector, useAppDispatch } from '@/store';
import { updateProfile, changePassword } from '@/store/slices/authSlice';
import type { User } from '@/types';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

const Profile: React.FC = () => {
  const dispatch = useAppDispatch();
  const { user, loading } = useAppSelector((state) => state.auth);

  const [profileForm] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const [notificationForm] = Form.useForm();

  const [activeTab, setActiveTab] = useState('profile');
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState(user?.avatar || '');

  // 模拟登录历史数据
  const loginHistory = [
    { id: 1, time: '2024-01-15 09:30:00', ip: '*************', device: 'Chrome on Windows', location: '上海' },
    { id: 2, time: '2024-01-14 14:20:00', ip: '*************', device: 'Safari on iPhone', location: '上海' },
    { id: 3, time: '2024-01-13 10:15:00', ip: '*************', device: 'Chrome on Windows', location: '上海' },
  ];

  const handleUpdateProfile = async (values: any) => {
    try {
      const updateData = {
        ...values,
        avatar: avatarUrl,
      };
      await dispatch(updateProfile(updateData)).unwrap();
      message.success('个人资料更新成功！');
    } catch (error) {
      message.error('更新失败，请重试');
    }
  };

  const handleChangePassword = async (values: any) => {
    try {
      await dispatch(changePassword({
        currentPassword: values.currentPassword,
        newPassword: values.newPassword,
      })).unwrap();

      setPasswordModalVisible(false);
      passwordForm.resetFields();
      message.success('密码修改成功！');
    } catch (error) {
      message.error('密码修改失败，请检查当前密码是否正确');
    }
  };

  const handleAvatarChange = (info: any) => {
    if (info.file.status === 'uploading') {
      return;
    }
    if (info.file.status === 'done') {
      // 这里应该是上传成功后的处理
      setAvatarUrl(info.file.response?.url || '');
      message.success('头像上传成功！');
    } else if (info.file.status === 'error') {
      message.error('头像上传失败！');
    }
  };

  const beforeUpload = (file: File) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('只能上传 JPG/PNG 格式的图片！');
      return false;
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('图片大小不能超过 2MB！');
      return false;
    }
    return false; // 阻止自动上传，这里应该手动处理上传
  };

  return (
    <div className="page-container">
      <div className="page-header">
        <Title level={3} className="page-title">个人资料</Title>
        <Text className="page-description">
          管理您的个人信息、安全设置和通知偏好
        </Text>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab={<span><UserOutlined />基本信息</span>} key="profile">
          <Row gutter={[24, 24]}>
            <Col span={16}>
              <Card title="个人信息">
                <Form
                  form={profileForm}
                  layout="vertical"
                  initialValues={{
                    fullName: user?.fullName,
                    email: user?.email,
                    phone: user?.phone,
                    department: user?.department,
                    position: user?.position,
                  }}
                  onFinish={handleUpdateProfile}
                >
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="fullName"
                        label="姓名"
                        rules={[{ required: true, message: '请输入姓名' }]}
                      >
                        <Input prefix={<UserOutlined />} />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="email"
                        label="邮箱"
                        rules={[
                          { required: true, message: '请输入邮箱' },
                          { type: 'email', message: '请输入有效的邮箱地址' }
                        ]}
                      >
                        <Input prefix={<MailOutlined />} />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="phone"
                        label="手机号码"
                        rules={[
                          { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' }
                        ]}
                      >
                        <Input prefix={<PhoneOutlined />} />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name="company" label="公司名称">
                        <Input prefix={<HomeOutlined />} />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Form.Item name="address" label="联系地址">
                    <TextArea rows={3} placeholder="请输入详细地址" />
                  </Form.Item>

                  <Form.Item>
                    <Button type="primary" htmlType="submit" loading={loading}>
                      保存修改
                    </Button>
                  </Form.Item>
                </Form>
              </Card>
            </Col>

            <Col span={8}>
              <Card title="头像设置">
                <div style={{ textAlign: 'center' }}>
                  <Avatar
                    size={120}
                    src={avatarUrl}
                    icon={<UserOutlined />}
                    style={{ marginBottom: 16 }}
                  />
                  <br />
                  <Upload
                    showUploadList={false}
                    beforeUpload={beforeUpload}
                    onChange={handleAvatarChange}
                  >
                    <Button icon={<UploadOutlined />}>
                      更换头像
                    </Button>
                  </Upload>
                  <div style={{ marginTop: 8 }}>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      支持 JPG、PNG 格式，文件大小不超过 2MB
                    </Text>
                  </div>
                </div>
              </Card>

              <Card title="账户统计" style={{ marginTop: 16 }}>
                <List size="small">
                  <List.Item>
                    <Text>注册时间</Text>
                    <Text>{user?.createdAt.split('T')[0]}</Text>
                  </List.Item>
                  <List.Item>
                    <Text>最后登录</Text>
                    <Text>{user?.lastLoginAt?.split('T')[0] || '未知'}</Text>
                  </List.Item>
                  <List.Item>
                    <Text>账户状态</Text>
                    <Tag color={user?.status === 'ACTIVE' ? 'green' : 'red'}>
                      {user?.status === 'ACTIVE' ? '正常' : '禁用'}
                    </Tag>
                  </List.Item>
                </List>
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab={<span><LockOutlined />安全设置</span>} key="security">
          <Row gutter={[24, 24]}>
            <Col span={12}>
              <Card title="密码安全">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div>
                    <Text strong>登录密码</Text>
                    <br />
                    <Text type="secondary">定期更换密码可以提高账户安全性</Text>
                  </div>
                  <Button
                    type="primary"
                    icon={<EditOutlined />}
                    onClick={() => setPasswordModalVisible(true)}
                  >
                    修改密码
                  </Button>
                </Space>
              </Card>

              <Card title="两步验证" style={{ marginTop: 16 }}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div>
                    <Text strong>手机验证</Text>
                    <br />
                    <Text type="secondary">登录时需要手机验证码</Text>
                  </div>
                  <Switch defaultChecked={false} />

                  <div style={{ marginTop: 16 }}>
                    <Text strong>邮箱验证</Text>
                    <br />
                    <Text type="secondary">重要操作时需要邮箱验证</Text>
                  </div>
                  <Switch defaultChecked={true} />
                </Space>
              </Card>
            </Col>

            <Col span={12}>
              <Card title="登录历史">
                <List
                  size="small"
                  dataSource={loginHistory}
                  renderItem={(item) => (
                    <List.Item>
                      <List.Item.Meta
                        title={
                          <Space>
                            <Text>{item.time}</Text>
                            <Tag size="small" color="blue">{item.location}</Tag>
                          </Space>
                        }
                        description={
                          <Space direction="vertical" size={0}>
                            <Text type="secondary">{item.device}</Text>
                            <Text type="secondary">{item.ip}</Text>
                          </Space>
                        }
                      />
                    </List.Item>
                  )}
                />
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab={<span><BellOutlined />通知设置</span>} key="notifications">
          <Card title="通知偏好设置">
            <Form
              form={notificationForm}
              layout="vertical"
              initialValues={{
                emailNotifications: true,
                smsNotifications: false,
                pushNotifications: true,
                projectUpdates: true,
                proposalNotifications: true,
                deliveryNotifications: true,
                marketingEmails: false,
              }}
            >
              <Row gutter={[24, 24]}>
                <Col span={12}>
                  <Card title="通知方式" size="small">
                    <Form.Item name="emailNotifications" valuePropName="checked">
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <div>
                          <Text strong>邮件通知</Text>
                          <br />
                          <Text type="secondary">通过邮件接收重要通知</Text>
                        </div>
                        <Switch />
                      </div>
                    </Form.Item>

                    <Divider />

                    <Form.Item name="smsNotifications" valuePropName="checked">
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <div>
                          <Text strong>短信通知</Text>
                          <br />
                          <Text type="secondary">通过短信接收紧急通知</Text>
                        </div>
                        <Switch />
                      </div>
                    </Form.Item>

                    <Divider />

                    <Form.Item name="pushNotifications" valuePropName="checked">
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <div>
                          <Text strong>推送通知</Text>
                          <br />
                          <Text type="secondary">浏览器推送通知</Text>
                        </div>
                        <Switch />
                      </div>
                    </Form.Item>
                  </Card>
                </Col>

                <Col span={12}>
                  <Card title="通知内容" size="small">
                    <Form.Item name="projectUpdates" valuePropName="checked">
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <div>
                          <Text strong>项目进度更新</Text>
                          <br />
                          <Text type="secondary">项目里程碑完成通知</Text>
                        </div>
                        <Switch />
                      </div>
                    </Form.Item>

                    <Divider />

                    <Form.Item name="proposalNotifications" valuePropName="checked">
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <div>
                          <Text strong>方案通知</Text>
                          <br />
                          <Text type="secondary">新方案和方案更新通知</Text>
                        </div>
                        <Switch />
                      </div>
                    </Form.Item>

                    <Divider />

                    <Form.Item name="deliveryNotifications" valuePropName="checked">
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <div>
                          <Text strong>交付通知</Text>
                          <br />
                          <Text type="secondary">交付和验收相关通知</Text>
                        </div>
                        <Switch />
                      </div>
                    </Form.Item>

                    <Divider />

                    <Form.Item name="marketingEmails" valuePropName="checked">
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <div>
                          <Text strong>营销邮件</Text>
                          <br />
                          <Text type="secondary">产品推广和优惠信息</Text>
                        </div>
                        <Switch />
                      </div>
                    </Form.Item>
                  </Card>
                </Col>
              </Row>

              <Form.Item style={{ marginTop: 24 }}>
                <Button type="primary" htmlType="submit">
                  保存通知设置
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>
      </Tabs>

      {/* 修改密码模态框 */}
      <Modal
        title="修改密码"
        open={passwordModalVisible}
        onCancel={() => {
          setPasswordModalVisible(false);
          passwordForm.resetFields();
        }}
        onOk={() => passwordForm.submit()}
        confirmLoading={loading}
        okText="确认修改"
        cancelText="取消"
      >
        <Alert
          message="密码安全提示"
          description="为了您的账户安全，请设置包含字母、数字的复杂密码，长度至少6位。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Form
          form={passwordForm}
          layout="vertical"
          onFinish={handleChangePassword}
        >
          <Form.Item
            name="currentPassword"
            label="当前密码"
            rules={[{ required: true, message: '请输入当前密码' }]}
          >
            <Input.Password placeholder="请输入当前密码" />
          </Form.Item>

          <Form.Item
            name="newPassword"
            label="新密码"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 6, message: '密码长度至少6位' },
              { pattern: /^(?=.*[a-zA-Z])(?=.*\d)/, message: '密码必须包含字母和数字' }
            ]}
          >
            <Input.Password placeholder="请输入新密码" />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            label="确认新密码"
            dependencies={['newPassword']}
            rules={[
              { required: true, message: '请确认新密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPassword') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password placeholder="请再次输入新密码" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Profile;
