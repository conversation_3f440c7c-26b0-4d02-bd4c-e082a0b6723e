import React, { useEffect } from 'react';
import { Form, Input, Button, Card, Typography, message, Radio, Space } from 'antd';
import { UserOutlined, MailOutlined, LockOutlined, PhoneOutlined, UserAddOutlined } from '@ant-design/icons';
import { Link, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '@/store';
import { register, clearError } from '@/store/slices/authSlice';
import { ROUTES } from '@/constants';
import LoadingSpinner from '@/components/common/LoadingSpinner';

const { Title, Text } = Typography;

interface RegisterFormData {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  fullName: string;
  phone?: string;
  userType: 'CUSTOMER' | 'INTERNAL';
}

const RegisterPage: React.FC = () => {
  const [form] = Form.useForm();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  
  const { loading, error } = useAppSelector((state) => state.auth);

  useEffect(() => {
    // 清除之前的错误
    dispatch(clearError());
  }, [dispatch]);

  useEffect(() => {
    // 显示错误消息
    if (error) {
      message.error(error);
    }
  }, [error]);

  const handleSubmit = async (values: RegisterFormData) => {
    try {
      const { confirmPassword, ...registerData } = values;
      await dispatch(register(registerData)).unwrap();
      message.success('注册成功！请登录您的账号。');
      navigate(ROUTES.LOGIN);
    } catch (error) {
      // 错误已经在 useEffect 中处理
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <Card
        style={{
          width: '100%',
          maxWidth: 500,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          borderRadius: 12
        }}
      >
        <div style={{ textAlign: 'center', marginBottom: 32 }}>
          <Title level={2} style={{ color: '#1890ff', marginBottom: 8 }}>
            注册账号
          </Title>
          <Text type="secondary">
            创建您的家具定制管理平台账号
          </Text>
        </div>

        <LoadingSpinner spinning={loading}>
          <Form
            form={form}
            name="register"
            onFinish={handleSubmit}
            layout="vertical"
            size="large"
            initialValues={{ userType: 'CUSTOMER' }}
          >
            <Form.Item
              name="userType"
              label="账号类型"
              rules={[{ required: true, message: '请选择账号类型' }]}
            >
              <Radio.Group>
                <Radio value="CUSTOMER">客户账号</Radio>
                <Radio value="INTERNAL">内部员工</Radio>
              </Radio.Group>
            </Form.Item>

            <Form.Item
              name="fullName"
              label="姓名"
              rules={[
                { required: true, message: '请输入姓名' },
                { min: 2, message: '姓名至少2个字符' }
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="请输入您的姓名"
              />
            </Form.Item>

            <Form.Item
              name="username"
              label="用户名"
              rules={[
                { required: true, message: '请输入用户名' },
                { min: 3, message: '用户名至少3个字符' },
                { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="请输入用户名"
                autoComplete="username"
              />
            </Form.Item>

            <Form.Item
              name="email"
              label="邮箱"
              rules={[
                { required: true, message: '请输入邮箱' },
                { type: 'email', message: '请输入有效的邮箱地址' }
              ]}
            >
              <Input
                prefix={<MailOutlined />}
                placeholder="请输入邮箱地址"
                autoComplete="email"
              />
            </Form.Item>

            <Form.Item
              name="phone"
              label="手机号码"
              rules={[
                { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' }
              ]}
            >
              <Input
                prefix={<PhoneOutlined />}
                placeholder="请输入手机号码（可选）"
                autoComplete="tel"
              />
            </Form.Item>

            <Form.Item
              name="password"
              label="密码"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6个字符' },
                { pattern: /^(?=.*[a-zA-Z])(?=.*\d)/, message: '密码必须包含字母和数字' }
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="请输入密码"
                autoComplete="new-password"
              />
            </Form.Item>

            <Form.Item
              name="confirmPassword"
              label="确认密码"
              dependencies={['password']}
              rules={[
                { required: true, message: '请确认密码' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('两次输入的密码不一致'));
                  },
                }),
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="请再次输入密码"
                autoComplete="new-password"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                icon={<UserAddOutlined />}
                block
                loading={loading}
              >
                注册
              </Button>
            </Form.Item>
          </Form>
        </LoadingSpinner>

        <div style={{ textAlign: 'center', marginTop: 16 }}>
          <Space>
            <Text type="secondary">已有账号？</Text>
            <Link to={ROUTES.LOGIN}>
              <Text>立即登录</Text>
            </Link>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default RegisterPage;
