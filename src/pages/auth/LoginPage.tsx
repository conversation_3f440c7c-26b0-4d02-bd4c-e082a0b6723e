import React, { useEffect } from 'react';
import { Form, Input, Button, Card, Typography, message, Divider, Space } from 'antd';
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '@/store';
import { login, clearError } from '@/store/slices/authSlice';
import { ROUTES } from '@/constants';
import LoadingSpinner from '@/components/common/LoadingSpinner';

const { Title, Text } = Typography;

interface LoginFormData {
  username: string;
  password: string;
}

const LoginPage: React.FC = () => {
  const [form] = Form.useForm();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  
  const { loading, error, isAuthenticated, user } = useAppSelector((state) => state.auth);

  // 获取重定向路径
  const from = (location.state as any)?.from?.pathname || '/';

  useEffect(() => {
    // 清除之前的错误
    dispatch(clearError());
  }, [dispatch]);

  useEffect(() => {
    // 如果已经登录，重定向到相应页面
    if (isAuthenticated && user) {
      const isCustomer = user.roles.some(role => role.name === 'CUSTOMER');
      const isAdmin = user.roles.some(role => 
        ['ADMIN', 'SUPER_ADMIN', 'SALES', 'DESIGNER', 'PROJECT_MANAGER', 'DELIVERY_MANAGER', 'CUSTOMER_SERVICE'].includes(role.name)
      );

      if (from !== '/') {
        navigate(from, { replace: true });
      } else if (isCustomer) {
        navigate('/customer/dashboard', { replace: true });
      } else if (isAdmin) {
        navigate('/admin/dashboard', { replace: true });
      }
    }
  }, [isAuthenticated, user, navigate, from]);

  useEffect(() => {
    // 显示错误消息
    if (error) {
      message.error(error);
    }
  }, [error]);

  const handleSubmit = async (values: LoginFormData) => {
    try {
      await dispatch(login(values)).unwrap();
      message.success('登录成功！');
    } catch (error) {
      // 错误已经在 useEffect 中处理
    }
  };

  // 演示账号登录
  const handleDemoLogin = (userType: 'customer' | 'admin') => {
    const demoCredentials = {
      customer: { username: 'demo_customer', password: 'demo123' },
      admin: { username: 'admin', password: 'admin123' }
    };
    
    form.setFieldsValue(demoCredentials[userType]);
    handleSubmit(demoCredentials[userType]);
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <Card
        style={{
          width: '100%',
          maxWidth: 400,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          borderRadius: 12
        }}
      >
        <div style={{ textAlign: 'center', marginBottom: 32 }}>
          <Title level={2} style={{ color: '#1890ff', marginBottom: 8 }}>
            家具定制管理平台
          </Title>
          <Text type="secondary">
            个性化家具定制服务全周期管理
          </Text>
        </div>

        <LoadingSpinner spinning={loading}>
          <Form
            form={form}
            name="login"
            onFinish={handleSubmit}
            layout="vertical"
            size="large"
          >
            <Form.Item
              name="username"
              label="用户名"
              rules={[
                { required: true, message: '请输入用户名' },
                { min: 3, message: '用户名至少3个字符' }
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="请输入用户名"
                autoComplete="username"
              />
            </Form.Item>

            <Form.Item
              name="password"
              label="密码"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6个字符' }
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="请输入密码"
                autoComplete="current-password"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                icon={<LoginOutlined />}
                block
                loading={loading}
              >
                登录
              </Button>
            </Form.Item>
          </Form>
        </LoadingSpinner>

        <Divider>演示账号</Divider>
        
        <Space direction="vertical" style={{ width: '100%' }}>
          <Button
            block
            onClick={() => handleDemoLogin('customer')}
            disabled={loading}
          >
            客户端演示 (demo_customer / demo123)
          </Button>
          <Button
            block
            onClick={() => handleDemoLogin('admin')}
            disabled={loading}
          >
            管理端演示 (admin / admin123)
          </Button>
        </Space>

        <Divider />

        <div style={{ textAlign: 'center' }}>
          <Space split={<span style={{ color: '#d9d9d9' }}>|</span>}>
            <Link to={ROUTES.REGISTER}>
              <Text>注册账号</Text>
            </Link>
            <Text type="secondary" style={{ cursor: 'pointer' }}>
              忘记密码
            </Text>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default LoginPage;
