import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Input,
  Select,
  Modal,
  Form,
  Row,
  Col,
  Descriptions,
  List,
  Avatar,
  Progress,
  Tabs,
  InputNumber,
  Upload,
  Image,
  Rate,
  message,
  Tooltip,
  Popconfirm,
  Badge,
  Statistic,
  Timeline,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  SendOutlined,
  CopyOutlined,
  FileTextOutlined,
  PictureOutlined,
  CalculatorOutlined,
  UserOutlined,
  CheckOutlined,
  CloseOutlined,
  HistoryOutlined,
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import { useAppSelector } from '@/store';
import { mockProposals, mockRequirements, mockUsers } from '@/data/mockData';
import { STATUS_COLORS } from '@/constants';
import type { Proposal, ProposalComponent } from '@/types';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

const Proposals: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [componentForm] = Form.useForm();

  const [proposals, setProposals] = useState<Proposal[]>(mockProposals);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [componentModalVisible, setComponentModalVisible] = useState(false);
  const [editingProposal, setEditingProposal] = useState<Proposal | null>(null);
  const [selectedProposal, setSelectedProposal] = useState<Proposal | null>(null);
  const [editingComponent, setEditingComponent] = useState<ProposalComponent | null>(null);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [designerFilter, setDesignerFilter] = useState<string>('');

  const { user } = useAppSelector((state) => state.auth);

  useEffect(() => {
    if (id) {
      const proposal = proposals.find(p => p.id === parseInt(id));
      if (proposal) {
        setSelectedProposal(proposal);
        setDetailModalVisible(true);
      }
    }
  }, [id, proposals]);

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      DRAFT: '草稿',
      REVIEWING: '审核中',
      SENT: '已发送',
      CONFIRMED: '已确认',
      REJECTED: '已拒绝',
    };
    return statusMap[status] || status;
  };

  const columns: ColumnsType<Proposal> = [
    {
      title: '方案信息',
      key: 'info',
      render: (_, record) => (
        <div>
          <div>
            <a onClick={() => handleViewDetail(record)}>{record.title}</a>
          </div>
          <Text type="secondary" ellipsis>
            {record.description}
          </Text>
        </div>
      ),
      width: 300,
    },
    {
      title: '关联需求',
      key: 'requirement',
      render: (_, record) => (
        <div>
          <div>{record.requirement?.title}</div>
          <Text type="secondary" style={{ fontSize: 12 }}>
            客户：{record.requirement?.customer?.name}
          </Text>
        </div>
      ),
    },
    {
      title: '总价',
      dataIndex: 'totalCost',
      key: 'totalCost',
      render: (cost) => (
        <div>
          <Text strong>¥{cost.toLocaleString()}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.components?.length || 0} 个组件
          </Text>
        </div>
      ),
      sorter: (a, b) => a.totalCost - b.totalCost,
    },
    {
      title: '利润率',
      dataIndex: 'profitMargin',
      key: 'profitMargin',
      render: (margin) => (
        <Progress
          percent={margin * 100}
          size="small"
          format={(percent) => `${percent?.toFixed(1)}%`}
          strokeColor={margin > 0.3 ? '#52c41a' : margin > 0.2 ? '#fa8c16' : '#ff4d4f'}
        />
      ),
      sorter: (a, b) => a.profitMargin - b.profitMargin,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={STATUS_COLORS[status as keyof typeof STATUS_COLORS]}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '设计师',
      key: 'designer',
      render: (_, record) => (
        record.createdUser ? (
          <Space>
            <Avatar size="small" src={record.createdUser.avatar}>
              {record.createdUser.fullName[0]}
            </Avatar>
            <Text>{record.createdUser.fullName}</Text>
          </Space>
        ) : (
          <Text type="secondary">未知</Text>
        )
      ),
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      render: (version) => (
        <Badge count={`v${version}`} style={{ backgroundColor: '#1890ff' }} />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => date.split('T')[0],
      sorter: (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="查看详情">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          {record.status === 'DRAFT' && (
            <Tooltip title="编辑">
              <Button
                type="link"
                icon={<EditOutlined />}
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
          )}
          {['DRAFT', 'REVIEWING'].includes(record.status) && (
            <Tooltip title="发送给客户">
              <Button
                type="link"
                icon={<SendOutlined />}
                style={{ color: '#52c41a' }}
                onClick={() => handleSend(record)}
              />
            </Tooltip>
          )}
          <Tooltip title="复制方案">
            <Button
              type="link"
              icon={<CopyOutlined />}
              onClick={() => handleDuplicate(record)}
            />
          </Tooltip>
          {record.status === 'DRAFT' && (
            <Tooltip title="删除">
              <Popconfirm
                title="确定要删除这个方案吗？"
                onConfirm={() => handleDelete(record)}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  type="link"
                  danger
                  icon={<DeleteOutlined />}
                />
              </Popconfirm>
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  const handleViewDetail = (proposal: Proposal) => {
    setSelectedProposal(proposal);
    setDetailModalVisible(true);
    navigate(`/admin/proposals/${proposal.id}`);
  };

  const handleEdit = (proposal: Proposal) => {
    setEditingProposal(proposal);
    form.setFieldsValue({
      title: proposal.title,
      description: proposal.description,
      requirementId: proposal.requirementId,
    });
    setModalVisible(true);
  };

  const handleAdd = () => {
    setEditingProposal(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleSend = async (proposal: Proposal) => {
    Modal.confirm({
      title: '确认发送方案',
      content: '确定要将此方案发送给客户吗？发送后客户将收到通知。',
      onOk: async () => {
        try {
          setLoading(true);
          await new Promise(resolve => setTimeout(resolve, 1000));

          const updatedProposals = proposals.map(p =>
            p.id === proposal.id
              ? { ...p, status: 'SENT' as const, updatedAt: new Date().toISOString() }
              : p
          );
          setProposals(updatedProposals);
          message.success('方案已发送给客户！');
        } catch (error) {
          message.error('发送失败，请重试');
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const handleDuplicate = async (proposal: Proposal) => {
    try {
      setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newProposal: Proposal = {
        ...proposal,
        id: Date.now(),
        title: `${proposal.title} (副本)`,
        status: 'DRAFT',
        version: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      setProposals([newProposal, ...proposals]);
      message.success('方案复制成功！');
    } catch (error) {
      message.error('复制失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (proposal: Proposal) => {
    try {
      setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));

      setProposals(proposals.filter(p => p.id !== proposal.id));
      message.success('方案删除成功！');
    } catch (error) {
      message.error('删除失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);

      if (editingProposal) {
        // 更新方案
        const updatedProposals = proposals.map(p =>
          p.id === editingProposal.id
            ? { ...p, ...values, updatedAt: new Date().toISOString() }
            : p
        );
        setProposals(updatedProposals);
        message.success('方案更新成功！');
      } else {
        // 创建新方案
        const requirement = mockRequirements.find(r => r.id === values.requirementId);
        const newProposal: Proposal = {
          id: Date.now(),
          requirementId: values.requirementId,
          requirement,
          title: values.title,
          description: values.description,
          totalCost: 0,
          profitMargin: 0.25,
          status: 'DRAFT',
          version: 1,
          components: [],
          comments: [],
          createdBy: user!.id,
          createdUser: user,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        setProposals([newProposal, ...proposals]);
        message.success('方案创建成功！');
      }

      setModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error('操作失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const filteredProposals = proposals.filter(proposal => {
    const matchesSearch = !searchText ||
      proposal.title.toLowerCase().includes(searchText.toLowerCase()) ||
      proposal.description.toLowerCase().includes(searchText.toLowerCase()) ||
      proposal.requirement?.customer?.name.toLowerCase().includes(searchText.toLowerCase());
    const matchesStatus = !statusFilter || proposal.status === statusFilter;
    const matchesDesigner = !designerFilter || proposal.createdBy?.toString() === designerFilter;

    return matchesSearch && matchesStatus && matchesDesigner;
  });

  return (
    <div className="page-container">
      <div className="page-header">
        <Title level={3} className="page-title">方案管理</Title>
        <Text className="page-description">
          管理设计方案，跟踪方案状态，协助客户确认方案
        </Text>
      </div>

      <Card>
        <div className="table-header">
          <Space wrap>
            <Input
              placeholder="搜索方案标题、描述或客户"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 250 }}
            />
            <Select
              placeholder="筛选状态"
              value={statusFilter}
              onChange={setStatusFilter}
              allowClear
              style={{ width: 120 }}
            >
              <Option value="DRAFT">草稿</Option>
              <Option value="REVIEWING">审核中</Option>
              <Option value="SENT">已发送</Option>
              <Option value="CONFIRMED">已确认</Option>
              <Option value="REJECTED">已拒绝</Option>
            </Select>
            <Select
              placeholder="筛选设计师"
              value={designerFilter}
              onChange={setDesignerFilter}
              allowClear
              style={{ width: 120 }}
            >
              {mockUsers.filter(u => u.roles.some(r => r.name === 'DESIGNER')).map(user => (
                <Option key={user.id} value={user.id.toString()}>
                  {user.fullName}
                </Option>
              ))}
            </Select>
          </Space>

          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            新建方案
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={filteredProposals}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredProposals.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 方案详情模态框 */}
      <Modal
        title="方案详情"
        open={detailModalVisible}
        onCancel={() => {
          setDetailModalVisible(false);
          setSelectedProposal(null);
          navigate('/admin/proposals');
        }}
        footer={null}
        width={1400}
        destroyOnClose
      >
        {selectedProposal && (
          <Tabs defaultActiveKey="overview">
            <TabPane tab="方案概览" key="overview">
              <Row gutter={[24, 24]}>
                <Col span={16}>
                  <Card title="方案信息" size="small">
                    <Descriptions column={2} size="small">
                      <Descriptions.Item label="方案标题">
                        {selectedProposal.title}
                      </Descriptions.Item>
                      <Descriptions.Item label="状态">
                        <Tag color={STATUS_COLORS[selectedProposal.status as keyof typeof STATUS_COLORS]}>
                          {getStatusText(selectedProposal.status)}
                        </Tag>
                      </Descriptions.Item>
                      <Descriptions.Item label="关联需求">
                        {selectedProposal.requirement?.title}
                      </Descriptions.Item>
                      <Descriptions.Item label="客户">
                        {selectedProposal.requirement?.customer?.name}
                      </Descriptions.Item>
                      <Descriptions.Item label="总价">
                        ¥{selectedProposal.totalCost.toLocaleString()}
                      </Descriptions.Item>
                      <Descriptions.Item label="利润率">
                        {(selectedProposal.profitMargin * 100).toFixed(1)}%
                      </Descriptions.Item>
                      <Descriptions.Item label="设计师">
                        {selectedProposal.createdUser?.fullName}
                      </Descriptions.Item>
                      <Descriptions.Item label="版本">
                        v{selectedProposal.version}
                      </Descriptions.Item>
                      <Descriptions.Item label="创建时间">
                        {selectedProposal.createdAt.split('T')[0]}
                      </Descriptions.Item>
                      <Descriptions.Item label="更新时间">
                        {selectedProposal.updatedAt.split('T')[0]}
                      </Descriptions.Item>
                    </Descriptions>

                    <div style={{ marginTop: 16 }}>
                      <Title level={5}>方案描述</Title>
                      <Paragraph>{selectedProposal.description}</Paragraph>
                    </div>
                  </Card>

                  <Card title="方案组件" size="small" style={{ marginTop: 16 }}>
                    <List
                      dataSource={selectedProposal.components}
                      renderItem={(component, index) => (
                        <List.Item
                          actions={[
                            <Text key="cost" strong>¥{component.totalCost.toLocaleString()}</Text>,
                            selectedProposal.status === 'DRAFT' && (
                              <Button
                                key="edit"
                                type="link"
                                icon={<EditOutlined />}
                                onClick={() => {
                                  setEditingComponent(component);
                                  componentForm.setFieldsValue(component);
                                  setComponentModalVisible(true);
                                }}
                              >
                                编辑
                              </Button>
                            )
                          ].filter(Boolean)}
                        >
                          <List.Item.Meta
                            avatar={
                              <Avatar
                                shape="square"
                                size={64}
                                src={component.imageUrl}
                                icon={<PictureOutlined />}
                              />
                            }
                            title={
                              <Space>
                                <Text strong>{component.componentName}</Text>
                                <Tag size="small">{component.componentType}</Tag>
                              </Space>
                            }
                            description={
                              <Space direction="vertical" size={4}>
                                <Text type="secondary">
                                  材料：{component.material}
                                </Text>
                                <Text type="secondary">
                                  尺寸：{component.dimensions}
                                </Text>
                                <Text type="secondary">
                                  数量：{component.quantity} | 单价：¥{component.unitCost.toLocaleString()}
                                </Text>
                                {component.description && (
                                  <Text type="secondary">
                                    {component.description}
                                  </Text>
                                )}
                              </Space>
                            }
                          />
                        </List.Item>
                      )}
                      footer={
                        selectedProposal.status === 'DRAFT' && (
                          <Button
                            type="dashed"
                            icon={<PlusOutlined />}
                            onClick={() => {
                              setEditingComponent(null);
                              componentForm.resetFields();
                              setComponentModalVisible(true);
                            }}
                            style={{ width: '100%' }}
                          >
                            添加组件
                          </Button>
                        )
                      }
                    />
                  </Card>
                </Col>

                <Col span={8}>
                  <Card title="成本分析" size="small">
                    <Row gutter={[16, 16]}>
                      <Col span={12}>
                        <Statistic
                          title="组件数量"
                          value={selectedProposal.components?.length || 0}
                          suffix="个"
                        />
                      </Col>
                      <Col span={12}>
                        <Statistic
                          title="总成本"
                          value={selectedProposal.totalCost}
                          prefix="¥"
                          precision={0}
                        />
                      </Col>
                      <Col span={12}>
                        <Statistic
                          title="利润率"
                          value={selectedProposal.profitMargin * 100}
                          suffix="%"
                          precision={1}
                          valueStyle={{
                            color: selectedProposal.profitMargin > 0.3 ? '#52c41a' :
                                   selectedProposal.profitMargin > 0.2 ? '#fa8c16' : '#ff4d4f'
                          }}
                        />
                      </Col>
                      <Col span={12}>
                        <Statistic
                          title="预估利润"
                          value={selectedProposal.totalCost * selectedProposal.profitMargin}
                          prefix="¥"
                          precision={0}
                        />
                      </Col>
                    </Row>
                  </Card>

                  <Card title="方案状态" size="small" style={{ marginTop: 16 }}>
                    <Timeline
                      size="small"
                      items={[
                        {
                          color: 'green',
                          children: '方案创建',
                          dot: <CheckOutlined />,
                        },
                        {
                          color: selectedProposal.status === 'DRAFT' ? 'gray' : 'green',
                          children: '内部审核',
                          dot: selectedProposal.status !== 'DRAFT' ? <CheckOutlined /> : <ClockCircleOutlined />,
                        },
                        {
                          color: ['SENT', 'CONFIRMED', 'REJECTED'].includes(selectedProposal.status) ? 'green' : 'gray',
                          children: '发送客户',
                          dot: ['SENT', 'CONFIRMED', 'REJECTED'].includes(selectedProposal.status) ? <CheckOutlined /> : <ClockCircleOutlined />,
                        },
                        {
                          color: selectedProposal.status === 'CONFIRMED' ? 'green' :
                                 selectedProposal.status === 'REJECTED' ? 'red' : 'gray',
                          children: '客户确认',
                          dot: selectedProposal.status === 'CONFIRMED' ? <CheckOutlined /> :
                               selectedProposal.status === 'REJECTED' ? <CloseOutlined /> : <ClockCircleOutlined />,
                        },
                      ]}
                    />
                  </Card>

                  <Card title="快速操作" size="small" style={{ marginTop: 16 }}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      {selectedProposal.status === 'DRAFT' && (
                        <>
                          <Button
                            icon={<EditOutlined />}
                            block
                            onClick={() => handleEdit(selectedProposal)}
                          >
                            编辑方案
                          </Button>
                          <Button
                            type="primary"
                            icon={<SendOutlined />}
                            block
                            onClick={() => handleSend(selectedProposal)}
                          >
                            发送给客户
                          </Button>
                        </>
                      )}
                      <Button
                        icon={<CopyOutlined />}
                        block
                        onClick={() => handleDuplicate(selectedProposal)}
                      >
                        复制方案
                      </Button>
                      <Button
                        icon={<CalculatorOutlined />}
                        block
                        onClick={() => message.info('成本计算功能开发中...')}
                      >
                        重新计算成本
                      </Button>
                      <Button
                        icon={<FileTextOutlined />}
                        block
                        onClick={() => message.info('生成PDF功能开发中...')}
                      >
                        生成PDF
                      </Button>
                    </Space>
                  </Card>
                </Col>
              </Row>
            </TabPane>

            <TabPane tab="客户反馈" key="feedback">
              <Card>
                {selectedProposal.comments && selectedProposal.comments.length > 0 ? (
                  <List
                    dataSource={selectedProposal.comments}
                    renderItem={(comment) => (
                      <List.Item>
                        <List.Item.Meta
                          avatar={<Avatar icon={<UserOutlined />}>{comment.user?.fullName?.[0]}</Avatar>}
                          title={
                            <Space>
                              <Text strong>{comment.user?.fullName}</Text>
                              <Tag size="small" color={comment.userType === 'CUSTOMER' ? 'blue' : 'green'}>
                                {comment.userType === 'CUSTOMER' ? '客户' : '内部'}
                              </Tag>
                              <Text type="secondary" style={{ fontSize: 12 }}>
                                {comment.createdAt.split('T')[0]}
                              </Text>
                            </Space>
                          }
                          description={
                            <div>
                              <Paragraph>{comment.content}</Paragraph>
                              {comment.rating && (
                                <div>
                                  <Text type="secondary">评分：</Text>
                                  <Rate disabled value={comment.rating} size="small" />
                                </div>
                              )}
                            </div>
                          }
                        />
                      </List.Item>
                    )}
                  />
                ) : (
                  <div style={{ textAlign: 'center', padding: '40px 0' }}>
                    <Text type="secondary">暂无客户反馈</Text>
                  </div>
                )}
              </Card>
            </TabPane>

            <TabPane tab="版本历史" key="history">
              <Card>
                <Timeline>
                  <Timeline.Item color="green">
                    <Text strong>v{selectedProposal.version} (当前版本)</Text>
                    <br />
                    <Text type="secondary">
                      {selectedProposal.createdUser?.fullName} 创建了方案
                    </Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      {selectedProposal.createdAt.split('T')[0]}
                    </Text>
                  </Timeline.Item>

                  {selectedProposal.status !== 'DRAFT' && (
                    <Timeline.Item color="blue">
                      <Text strong>方案审核</Text>
                      <br />
                      <Text type="secondary">
                        方案通过内部审核
                      </Text>
                    </Timeline.Item>
                  )}

                  {['SENT', 'CONFIRMED', 'REJECTED'].includes(selectedProposal.status) && (
                    <Timeline.Item color="orange">
                      <Text strong>发送客户</Text>
                      <br />
                      <Text type="secondary">
                        方案已发送给客户确认
                      </Text>
                    </Timeline.Item>
                  )}

                  {selectedProposal.status === 'CONFIRMED' && (
                    <Timeline.Item color="green">
                      <Text strong>客户确认</Text>
                      <br />
                      <Text type="secondary">
                        客户已确认方案，准备进入项目阶段
                      </Text>
                    </Timeline.Item>
                  )}

                  {selectedProposal.status === 'REJECTED' && (
                    <Timeline.Item color="red">
                      <Text strong>客户拒绝</Text>
                      <br />
                      <Text type="secondary">
                        客户拒绝了方案，需要重新设计
                      </Text>
                    </Timeline.Item>
                  )}
                </Timeline>
              </Card>
            </TabPane>
          </Tabs>
        )}
      </Modal>

      {/* 新建/编辑方案模态框 */}
      <Modal
        title={editingProposal ? '编辑方案' : '新建方案'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingProposal(null);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        confirmLoading={loading}
        okText={editingProposal ? '更新' : '创建'}
        cancelText="取消"
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="requirementId"
            label="关联需求"
            rules={[{ required: true, message: '请选择关联需求' }]}
          >
            <Select placeholder="请选择关联需求" disabled={!!editingProposal}>
              {mockRequirements.map(req => (
                <Option key={req.id} value={req.id}>
                  <div>
                    <div>{req.title}</div>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      客户：{req.customer?.name} | 预算：{req.budgetRange}
                    </Text>
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="title"
            label="方案标题"
            rules={[{ required: true, message: '请输入方案标题' }]}
          >
            <Input placeholder="请输入方案标题" />
          </Form.Item>

          <Form.Item
            name="description"
            label="方案描述"
            rules={[{ required: true, message: '请输入方案描述' }]}
          >
            <TextArea
              rows={4}
              placeholder="请详细描述方案的设计理念、特色和优势..."
              maxLength={1000}
              showCount
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 组件编辑模态框 */}
      <Modal
        title={editingComponent ? '编辑组件' : '添加组件'}
        open={componentModalVisible}
        onCancel={() => {
          setComponentModalVisible(false);
          setEditingComponent(null);
          componentForm.resetFields();
        }}
        onOk={() => componentForm.submit()}
        confirmLoading={loading}
        okText={editingComponent ? '更新' : '添加'}
        cancelText="取消"
        width={800}
      >
        <Form
          form={componentForm}
          layout="vertical"
          onFinish={(values) => {
            // 这里应该处理组件的添加/编辑逻辑
            message.success(editingComponent ? '组件更新成功！' : '组件添加成功！');
            setComponentModalVisible(false);
            setEditingComponent(null);
            componentForm.resetFields();
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="componentName"
                label="组件名称"
                rules={[{ required: true, message: '请输入组件名称' }]}
              >
                <Input placeholder="请输入组件名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="componentType"
                label="组件类型"
                rules={[{ required: true, message: '请选择组件类型' }]}
              >
                <Select placeholder="请选择组件类型">
                  <Option value="沙发">沙发</Option>
                  <Option value="茶几">茶几</Option>
                  <Option value="电视柜">电视柜</Option>
                  <Option value="床">床</Option>
                  <Option value="衣柜">衣柜</Option>
                  <Option value="床头柜">床头柜</Option>
                  <Option value="餐桌">餐桌</Option>
                  <Option value="餐椅">餐椅</Option>
                  <Option value="其他">其他</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="material"
                label="材料"
                rules={[{ required: true, message: '请输入材料' }]}
              >
                <Input placeholder="请输入材料信息" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="dimensions"
                label="尺寸"
                rules={[{ required: true, message: '请输入尺寸' }]}
              >
                <Input placeholder="长×宽×高 (cm)" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="quantity"
                label="数量"
                rules={[{ required: true, message: '请输入数量' }]}
              >
                <InputNumber min={1} placeholder="数量" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="unitCost"
                label="单价"
                rules={[{ required: true, message: '请输入单价' }]}
              >
                <InputNumber
                  min={0}
                  precision={2}
                  placeholder="单价"
                  style={{ width: '100%' }}
                  formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => value!.replace(/¥\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="totalCost"
                label="总价"
              >
                <InputNumber
                  disabled
                  precision={2}
                  style={{ width: '100%' }}
                  formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="组件描述"
          >
            <TextArea
              rows={3}
              placeholder="请描述组件的特色、工艺等详细信息..."
              maxLength={500}
              showCount
            />
          </Form.Item>

          <Form.Item
            name="imageUrl"
            label="组件图片"
          >
            <Upload
              listType="picture-card"
              showUploadList={false}
              beforeUpload={() => false}
              onChange={(info) => {
                // 处理图片上传
                message.info('图片上传功能开发中...');
              }}
            >
              <div>
                <PictureOutlined />
                <div style={{ marginTop: 8 }}>上传图片</div>
              </div>
            </Upload>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Proposals;
