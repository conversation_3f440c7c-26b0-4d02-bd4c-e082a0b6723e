import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Input,
  Select,
  Modal,
  Form,
  Row,
  Col,
  Descriptions,
  Progress,
  Tabs,
  List,
  Avatar,
  Timeline,
  DatePicker,
  Statistic,
  Alert,
  message,
  Tooltip,
  Popconfirm,
  Badge,
  Calendar,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  TeamOutlined,
  CalendarOutlined,
  FileTextOutlined,
  WarningOutlined,
  ClockCircleOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import { useAppSelector } from '@/store';
import { mockProjects, mockUsers } from '@/data/mockData';
import { STATUS_COLORS } from '@/constants';
import type { Project, ProjectMilestone, ProjectTask } from '@/types';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

const Projects: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [milestoneForm] = Form.useForm();
  const [taskForm] = Form.useForm();

  const [projects, setProjects] = useState<Project[]>(mockProjects);
  const [loading, setLoading] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [milestoneModalVisible, setMilestoneModalVisible] = useState(false);
  const [taskModalVisible, setTaskModalVisible] = useState(false);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [editingMilestone, setEditingMilestone] = useState<ProjectMilestone | null>(null);
  const [editingTask, setEditingTask] = useState<ProjectTask | null>(null);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [managerFilter, setManagerFilter] = useState<string>('');

  const { user } = useAppSelector((state) => state.auth);

  useEffect(() => {
    if (id) {
      const project = projects.find(p => p.id === parseInt(id));
      if (project) {
        setSelectedProject(project);
        setDetailModalVisible(true);
      }
    }
  }, [id, projects]);

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      PLANNING: '规划中',
      IN_PROGRESS: '进行中',
      DELAYED: '已延期',
      COMPLETED: '已完成',
      CANCELLED: '已取消',
    };
    return statusMap[status] || status;
  };

  const getStatusIcon = (status: string) => {
    const iconMap: Record<string, React.ReactNode> = {
      PLANNING: <ClockCircleOutlined />,
      IN_PROGRESS: <PlayCircleOutlined />,
      DELAYED: <WarningOutlined style={{ color: '#ff4d4f' }} />,
      COMPLETED: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      CANCELLED: <CloseCircleOutlined style={{ color: '#8c8c8c' }} />,
    };
    return iconMap[status] || <ClockCircleOutlined />;
  };

  const columns: ColumnsType<Project> = [
    {
      title: '项目信息',
      key: 'info',
      render: (_, record) => (
        <div>
          <div>
            <a onClick={() => handleViewDetail(record)}>{record.name}</a>
          </div>
          <Text type="secondary" ellipsis>
            {record.description}
          </Text>
        </div>
      ),
      width: 300,
    },
    {
      title: '客户',
      key: 'customer',
      render: (_, record) => (
        <div>
          <div>{record.proposal?.requirement?.customer?.name}</div>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.proposal?.requirement?.customer?.company}
          </Text>
        </div>
      ),
    },
    {
      title: '进度',
      dataIndex: 'progressPercentage',
      key: 'progress',
      render: (progress, record) => (
        <Space direction="vertical" size={4} style={{ width: '100%' }}>
          <Progress
            percent={progress}
            size="small"
            status={record.status === 'DELAYED' ? 'exception' : 'active'}
          />
          <Text type="secondary" style={{ fontSize: 12 }}>
            {progress}% 完成
          </Text>
        </Space>
      ),
      width: 150,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag
          icon={getStatusIcon(status)}
          color={STATUS_COLORS[status as keyof typeof STATUS_COLORS]}
        >
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '项目经理',
      key: 'manager',
      render: (_, record) => (
        record.projectManager ? (
          <Space>
            <Avatar size="small" src={record.projectManager.avatar}>
              {record.projectManager.fullName[0]}
            </Avatar>
            <Text>{record.projectManager.fullName}</Text>
          </Space>
        ) : (
          <Text type="secondary">未分配</Text>
        )
      ),
    },
    {
      title: '时间安排',
      key: 'schedule',
      render: (_, record) => {
        const isOverdue = new Date(record.endDate) < new Date() && record.status !== 'COMPLETED';
        return (
          <Space direction="vertical" size={0}>
            <Text type="secondary" style={{ fontSize: 12 }}>
              开始：{record.startDate}
            </Text>
            <Text type={isOverdue ? 'danger' : 'secondary'} style={{ fontSize: 12 }}>
              预计：{record.endDate}
            </Text>
            {record.actualEndDate && (
              <Text type="secondary" style={{ fontSize: 12 }}>
                实际：{record.actualEndDate}
              </Text>
            )}
          </Space>
        );
      },
      sorter: (a, b) => new Date(a.endDate).getTime() - new Date(b.endDate).getTime(),
    },
    {
      title: '里程碑',
      key: 'milestones',
      render: (_, record) => {
        const total = record.milestones.length;
        const completed = record.milestones.filter(m => m.status === 'COMPLETED').length;
        return (
          <Space>
            <Text>{completed}/{total}</Text>
            <Progress
              type="circle"
              size={30}
              percent={total > 0 ? (completed / total) * 100 : 0}
              format={() => ''}
            />
          </Space>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="查看详情">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          {record.status === 'PLANNING' && (
            <Tooltip title="开始项目">
              <Button
                type="link"
                icon={<PlayCircleOutlined />}
                style={{ color: '#52c41a' }}
                onClick={() => handleStartProject(record)}
              />
            </Tooltip>
          )}
          {record.status === 'IN_PROGRESS' && (
            <Tooltip title="暂停项目">
              <Button
                type="link"
                icon={<PauseCircleOutlined />}
                style={{ color: '#fa8c16' }}
                onClick={() => handlePauseProject(record)}
              />
            </Tooltip>
          )}
          {['IN_PROGRESS', 'DELAYED'].includes(record.status) && (
            <Tooltip title="标记完成">
              <Button
                type="link"
                icon={<CheckCircleOutlined />}
                style={{ color: '#52c41a' }}
                onClick={() => handleCompleteProject(record)}
              />
            </Tooltip>
          )}
          <Tooltip title="项目团队">
            <Button
              type="link"
              icon={<TeamOutlined />}
              onClick={() => handleManageTeam(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const handleViewDetail = (project: Project) => {
    setSelectedProject(project);
    setDetailModalVisible(true);
    navigate(`/admin/projects/${project.id}`);
  };

  const handleStartProject = async (project: Project) => {
    Modal.confirm({
      title: '确认开始项目',
      content: '确定要开始这个项目吗？项目状态将变更为进行中。',
      onOk: async () => {
        try {
          setLoading(true);
          await new Promise(resolve => setTimeout(resolve, 1000));

          const updatedProjects = projects.map(p =>
            p.id === project.id
              ? {
                  ...p,
                  status: 'IN_PROGRESS' as const,
                  actualStartDate: new Date().toISOString().split('T')[0],
                  updatedAt: new Date().toISOString()
                }
              : p
          );
          setProjects(updatedProjects);
          message.success('项目已开始！');
        } catch (error) {
          message.error('操作失败，请重试');
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const handlePauseProject = async (project: Project) => {
    Modal.confirm({
      title: '确认暂停项目',
      content: '确定要暂停这个项目吗？',
      onOk: async () => {
        try {
          setLoading(true);
          await new Promise(resolve => setTimeout(resolve, 1000));

          const updatedProjects = projects.map(p =>
            p.id === project.id
              ? { ...p, status: 'DELAYED' as const, updatedAt: new Date().toISOString() }
              : p
          );
          setProjects(updatedProjects);
          message.success('项目已暂停！');
        } catch (error) {
          message.error('操作失败，请重试');
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const handleCompleteProject = async (project: Project) => {
    Modal.confirm({
      title: '确认完成项目',
      content: '确定要标记这个项目为已完成吗？',
      onOk: async () => {
        try {
          setLoading(true);
          await new Promise(resolve => setTimeout(resolve, 1000));

          const updatedProjects = projects.map(p =>
            p.id === project.id
              ? {
                  ...p,
                  status: 'COMPLETED' as const,
                  progressPercentage: 100,
                  actualEndDate: new Date().toISOString().split('T')[0],
                  updatedAt: new Date().toISOString()
                }
              : p
          );
          setProjects(updatedProjects);
          message.success('项目已完成！');
        } catch (error) {
          message.error('操作失败，请重试');
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const handleManageTeam = (project: Project) => {
    message.info('团队管理功能开发中...');
  };

  const filteredProjects = projects.filter(project => {
    const matchesSearch = !searchText ||
      project.name.toLowerCase().includes(searchText.toLowerCase()) ||
      project.description.toLowerCase().includes(searchText.toLowerCase()) ||
      project.proposal?.requirement?.customer?.name.toLowerCase().includes(searchText.toLowerCase());
    const matchesStatus = !statusFilter || project.status === statusFilter;
    const matchesManager = !managerFilter || project.projectManagerId?.toString() === managerFilter;

    return matchesSearch && matchesStatus && matchesManager;
  });

  return (
    <div className="page-container">
      <div className="page-header">
        <Title level={3} className="page-title">项目管理</Title>
        <Text className="page-description">
          管理项目进度，跟踪里程碑完成情况，协调项目团队
        </Text>
      </div>

      <Card>
        <div className="table-header">
          <Space wrap>
            <Input
              placeholder="搜索项目名称、描述或客户"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 250 }}
            />
            <Select
              placeholder="筛选状态"
              value={statusFilter}
              onChange={setStatusFilter}
              allowClear
              style={{ width: 120 }}
            >
              <Option value="PLANNING">规划中</Option>
              <Option value="IN_PROGRESS">进行中</Option>
              <Option value="DELAYED">已延期</Option>
              <Option value="COMPLETED">已完成</Option>
              <Option value="CANCELLED">已取消</Option>
            </Select>
            <Select
              placeholder="筛选项目经理"
              value={managerFilter}
              onChange={setManagerFilter}
              allowClear
              style={{ width: 120 }}
            >
              {mockUsers.filter(u => u.roles.some(r => r.name === 'PROJECT_MANAGER')).map(user => (
                <Option key={user.id} value={user.id.toString()}>
                  {user.fullName}
                </Option>
              ))}
            </Select>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={filteredProjects}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredProjects.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 项目详情模态框 */}
      <Modal
        title="项目详情"
        open={detailModalVisible}
        onCancel={() => {
          setDetailModalVisible(false);
          setSelectedProject(null);
          navigate('/admin/projects');
        }}
        footer={null}
        width={1400}
        destroyOnClose
      >
        {selectedProject && (
          <Tabs defaultActiveKey="overview">
            <TabPane tab="项目概览" key="overview">
              <Row gutter={[24, 24]}>
                <Col span={16}>
                  <Card title="项目信息" size="small">
                    <Descriptions column={2} size="small">
                      <Descriptions.Item label="项目名称">
                        {selectedProject.name}
                      </Descriptions.Item>
                      <Descriptions.Item label="状态">
                        <Tag
                          icon={getStatusIcon(selectedProject.status)}
                          color={STATUS_COLORS[selectedProject.status as keyof typeof STATUS_COLORS]}
                        >
                          {getStatusText(selectedProject.status)}
                        </Tag>
                      </Descriptions.Item>
                      <Descriptions.Item label="客户">
                        {selectedProject.proposal?.requirement?.customer?.name}
                      </Descriptions.Item>
                      <Descriptions.Item label="公司">
                        {selectedProject.proposal?.requirement?.customer?.company}
                      </Descriptions.Item>
                      <Descriptions.Item label="项目经理">
                        {selectedProject.projectManager?.fullName || '未分配'}
                      </Descriptions.Item>
                      <Descriptions.Item label="进度">
                        <Progress
                          percent={selectedProject.progressPercentage}
                          size="small"
                          status={selectedProject.status === 'DELAYED' ? 'exception' : 'active'}
                        />
                      </Descriptions.Item>
                      <Descriptions.Item label="计划开始">
                        {selectedProject.startDate}
                      </Descriptions.Item>
                      <Descriptions.Item label="计划完成">
                        {selectedProject.endDate}
                      </Descriptions.Item>
                      {selectedProject.actualStartDate && (
                        <Descriptions.Item label="实际开始">
                          {selectedProject.actualStartDate}
                        </Descriptions.Item>
                      )}
                      {selectedProject.actualEndDate && (
                        <Descriptions.Item label="实际完成">
                          {selectedProject.actualEndDate}
                        </Descriptions.Item>
                      )}
                    </Descriptions>

                    <div style={{ marginTop: 16 }}>
                      <Title level={5}>项目描述</Title>
                      <Paragraph>{selectedProject.description}</Paragraph>
                    </div>
                  </Card>

                  <Card title="里程碑进度" size="small" style={{ marginTop: 16 }}>
                    <Timeline>
                      {selectedProject.milestones.map((milestone) => (
                        <Timeline.Item
                          key={milestone.id}
                          color={
                            milestone.status === 'COMPLETED' ? 'green' :
                            milestone.status === 'IN_PROGRESS' ? 'blue' :
                            milestone.status === 'DELAYED' ? 'red' : 'gray'
                          }
                          dot={
                            milestone.status === 'COMPLETED' ? <CheckCircleOutlined /> :
                            milestone.status === 'IN_PROGRESS' ? <ClockCircleOutlined /> :
                            milestone.status === 'DELAYED' ? <WarningOutlined /> :
                            <ClockCircleOutlined />
                          }
                        >
                          <div>
                            <Space>
                              <Text strong>{milestone.name}</Text>
                              <Tag
                                size="small"
                                color={STATUS_COLORS[milestone.status as keyof typeof STATUS_COLORS]}
                              >
                                {getStatusText(milestone.status)}
                              </Tag>
                              <Button
                                type="link"
                                size="small"
                                icon={<EditOutlined />}
                                onClick={() => {
                                  setEditingMilestone(milestone);
                                  milestoneForm.setFieldsValue(milestone);
                                  setMilestoneModalVisible(true);
                                }}
                              >
                                编辑
                              </Button>
                            </Space>
                            <br />
                            <Text type="secondary">{milestone.description}</Text>
                            <br />
                            <Space>
                              <Text type="secondary">
                                计划：{milestone.plannedDate}
                              </Text>
                              {milestone.actualDate && (
                                <Text type="secondary">
                                  实际：{milestone.actualDate}
                                </Text>
                              )}
                            </Space>
                          </div>
                        </Timeline.Item>
                      ))}
                    </Timeline>

                    <Button
                      type="dashed"
                      icon={<PlusOutlined />}
                      onClick={() => {
                        setEditingMilestone(null);
                        milestoneForm.resetFields();
                        setMilestoneModalVisible(true);
                      }}
                      style={{ width: '100%', marginTop: 16 }}
                    >
                      添加里程碑
                    </Button>
                  </Card>
                </Col>

                <Col span={8}>
                  <Card title="项目统计" size="small">
                    <Row gutter={[16, 16]}>
                      <Col span={12}>
                        <Statistic
                          title="总里程碑"
                          value={selectedProject.milestones.length}
                          suffix="个"
                        />
                      </Col>
                      <Col span={12}>
                        <Statistic
                          title="已完成"
                          value={selectedProject.milestones.filter(m => m.status === 'COMPLETED').length}
                          suffix="个"
                        />
                      </Col>
                      <Col span={12}>
                        <Statistic
                          title="进行中"
                          value={selectedProject.milestones.filter(m => m.status === 'IN_PROGRESS').length}
                          suffix="个"
                        />
                      </Col>
                      <Col span={12}>
                        <Statistic
                          title="延期"
                          value={selectedProject.milestones.filter(m => m.status === 'DELAYED').length}
                          suffix="个"
                          valueStyle={{ color: '#ff4d4f' }}
                        />
                      </Col>
                    </Row>
                  </Card>

                  {selectedProject.status === 'DELAYED' && (
                    <Alert
                      message="项目延期提醒"
                      description="项目进度有所延期，请及时调整计划或增加资源投入。"
                      type="warning"
                      showIcon
                      style={{ marginTop: 16 }}
                    />
                  )}

                  <Card title="快速操作" size="small" style={{ marginTop: 16 }}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      {selectedProject.status === 'PLANNING' && (
                        <Button
                          type="primary"
                          icon={<PlayCircleOutlined />}
                          block
                          onClick={() => handleStartProject(selectedProject)}
                        >
                          开始项目
                        </Button>
                      )}
                      {selectedProject.status === 'IN_PROGRESS' && (
                        <>
                          <Button
                            icon={<PauseCircleOutlined />}
                            block
                            onClick={() => handlePauseProject(selectedProject)}
                          >
                            暂停项目
                          </Button>
                          <Button
                            type="primary"
                            icon={<CheckCircleOutlined />}
                            block
                            onClick={() => handleCompleteProject(selectedProject)}
                          >
                            标记完成
                          </Button>
                        </>
                      )}
                      <Button
                        icon={<TeamOutlined />}
                        block
                        onClick={() => handleManageTeam(selectedProject)}
                      >
                        管理团队
                      </Button>
                      <Button
                        icon={<FileTextOutlined />}
                        block
                        onClick={() => message.info('生成报告功能开发中...')}
                      >
                        生成报告
                      </Button>
                    </Space>
                  </Card>
                </Col>
              </Row>
            </TabPane>

            <TabPane tab="任务管理" key="tasks">
              <Card>
                <div style={{ marginBottom: 16 }}>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => {
                      setEditingTask(null);
                      taskForm.resetFields();
                      setTaskModalVisible(true);
                    }}
                  >
                    添加任务
                  </Button>
                </div>

                <List
                  dataSource={selectedProject.tasks || []}
                  renderItem={(task) => (
                    <List.Item
                      actions={[
                        <Tag color={STATUS_COLORS[task.status as keyof typeof STATUS_COLORS]}>
                          {getStatusText(task.status)}
                        </Tag>,
                        <Button
                          type="link"
                          icon={<EditOutlined />}
                          onClick={() => {
                            setEditingTask(task);
                            taskForm.setFieldsValue(task);
                            setTaskModalVisible(true);
                          }}
                        >
                          编辑
                        </Button>
                      ]}
                    >
                      <List.Item.Meta
                        avatar={
                          task.assignedUser ? (
                            <Avatar src={task.assignedUser.avatar}>
                              {task.assignedUser.fullName[0]}
                            </Avatar>
                          ) : (
                            <Avatar icon={<UserOutlined />} />
                          )
                        }
                        title={
                          <Space>
                            <Text strong>{task.taskName}</Text>
                            <Tag size="small" color={
                              task.priority === 'HIGH' ? 'red' :
                              task.priority === 'MEDIUM' ? 'orange' : 'green'
                            }>
                              {task.priority === 'HIGH' ? '高' :
                               task.priority === 'MEDIUM' ? '中' : '低'}
                            </Tag>
                          </Space>
                        }
                        description={
                          <Space direction="vertical" size={4}>
                            <Text type="secondary">{task.description}</Text>
                            <Space>
                              <Text type="secondary">
                                负责人：{task.assignedUser?.fullName || '未分配'}
                              </Text>
                              <Text type="secondary">
                                截止：{task.dueDate}
                              </Text>
                            </Space>
                          </Space>
                        }
                      />
                    </List.Item>
                  )}
                />
              </Card>
            </TabPane>

            <TabPane tab="项目日历" key="calendar">
              <Card>
                <Calendar
                  dateCellRender={(value) => {
                    const dateStr = value.format('YYYY-MM-DD');
                    const milestones = selectedProject.milestones.filter(m =>
                      m.plannedDate === dateStr || m.actualDate === dateStr
                    );

                    return (
                      <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
                        {milestones.map((milestone, index) => (
                          <li key={index}>
                            <Badge
                              status={milestone.status === 'COMPLETED' ? 'success' :
                                     milestone.status === 'DELAYED' ? 'error' : 'processing'}
                              text={milestone.name}
                            />
                          </li>
                        ))}
                      </ul>
                    );
                  }}
                />
              </Card>
            </TabPane>

            <TabPane tab="团队成员" key="team">
              <Card>
                <List
                  dataSource={[
                    {
                      id: selectedProject.projectManagerId,
                      name: selectedProject.projectManager?.fullName || '项目经理',
                      role: '项目经理',
                      avatar: selectedProject.projectManager?.avatar,
                      email: selectedProject.projectManager?.email,
                      phone: selectedProject.projectManager?.phone,
                    },
                    // 可以添加更多团队成员
                  ]}
                  renderItem={(member) => (
                    <List.Item
                      actions={[
                        <Button type="link">联系</Button>
                      ]}
                    >
                      <List.Item.Meta
                        avatar={<Avatar src={member.avatar}>{member.name[0]}</Avatar>}
                        title={member.name}
                        description={
                          <Space direction="vertical" size={4}>
                            <Text type="secondary">{member.role}</Text>
                            {member.email && (
                              <Text type="secondary">{member.email}</Text>
                            )}
                            {member.phone && (
                              <Text type="secondary">{member.phone}</Text>
                            )}
                          </Space>
                        }
                      />
                    </List.Item>
                  )}
                />
              </Card>
            </TabPane>
          </Tabs>
        )}
      </Modal>

      {/* 里程碑编辑模态框 */}
      <Modal
        title={editingMilestone ? '编辑里程碑' : '添加里程碑'}
        open={milestoneModalVisible}
        onCancel={() => {
          setMilestoneModalVisible(false);
          setEditingMilestone(null);
          milestoneForm.resetFields();
        }}
        onOk={() => milestoneForm.submit()}
        confirmLoading={loading}
        okText={editingMilestone ? '更新' : '添加'}
        cancelText="取消"
      >
        <Form
          form={milestoneForm}
          layout="vertical"
          onFinish={(values) => {
            message.success(editingMilestone ? '里程碑更新成功！' : '里程碑添加成功！');
            setMilestoneModalVisible(false);
            setEditingMilestone(null);
            milestoneForm.resetFields();
          }}
        >
          <Form.Item
            name="name"
            label="里程碑名称"
            rules={[{ required: true, message: '请输入里程碑名称' }]}
          >
            <Input placeholder="请输入里程碑名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入描述' }]}
          >
            <TextArea rows={3} placeholder="请描述里程碑的具体内容和目标" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="plannedDate"
                label="计划日期"
                rules={[{ required: true, message: '请选择计划日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select placeholder="请选择状态">
                  <Option value="PLANNING">规划中</Option>
                  <Option value="IN_PROGRESS">进行中</Option>
                  <Option value="COMPLETED">已完成</Option>
                  <Option value="DELAYED">已延期</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 任务编辑模态框 */}
      <Modal
        title={editingTask ? '编辑任务' : '添加任务'}
        open={taskModalVisible}
        onCancel={() => {
          setTaskModalVisible(false);
          setEditingTask(null);
          taskForm.resetFields();
        }}
        onOk={() => taskForm.submit()}
        confirmLoading={loading}
        okText={editingTask ? '更新' : '添加'}
        cancelText="取消"
        width={600}
      >
        <Form
          form={taskForm}
          layout="vertical"
          onFinish={(values) => {
            message.success(editingTask ? '任务更新成功！' : '任务添加成功！');
            setTaskModalVisible(false);
            setEditingTask(null);
            taskForm.resetFields();
          }}
        >
          <Form.Item
            name="taskName"
            label="任务名称"
            rules={[{ required: true, message: '请输入任务名称' }]}
          >
            <Input placeholder="请输入任务名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="任务描述"
            rules={[{ required: true, message: '请输入任务描述' }]}
          >
            <TextArea rows={3} placeholder="请详细描述任务内容和要求" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="assignedTo"
                label="负责人"
                rules={[{ required: true, message: '请选择负责人' }]}
              >
                <Select placeholder="请选择负责人">
                  {mockUsers.map(user => (
                    <Option key={user.id} value={user.id}>
                      <Space>
                        <Avatar size="small" src={user.avatar}>
                          {user.fullName[0]}
                        </Avatar>
                        {user.fullName}
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="priority"
                label="优先级"
                rules={[{ required: true, message: '请选择优先级' }]}
              >
                <Select placeholder="请选择优先级">
                  <Option value="LOW">低</Option>
                  <Option value="MEDIUM">中</Option>
                  <Option value="HIGH">高</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="dueDate"
                label="截止日期"
                rules={[{ required: true, message: '请选择截止日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select placeholder="请选择状态">
                  <Option value="PLANNING">规划中</Option>
                  <Option value="IN_PROGRESS">进行中</Option>
                  <Option value="COMPLETED">已完成</Option>
                  <Option value="DELAYED">已延期</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default Projects;
