import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Input,
  Select,
  Modal,
  Form,
  Row,
  Col,
  Descriptions,
  Avatar,
  Statistic,
  Tabs,
  List,
  Progress,
  DatePicker,
  Upload,
  message,
  Tooltip,
  Popconfirm,
  Badge,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  HomeOutlined,
  UploadOutlined,
  DownloadOutlined,
  StarOutlined,
  ProjectOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import { useAppDispatch } from '@/store';
import { mockCustomers, mockProjects, mockRequirements } from '@/data/mockData';
import type { Customer } from '@/types';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

const Customers: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [form] = Form.useForm();

  const [customers, setCustomers] = useState<Customer[]>(mockCustomers);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [searchText, setSearchText] = useState('');
  const [industryFilter, setIndustryFilter] = useState<string>('');
  const [levelFilter, setLevelFilter] = useState<string>('');

  useEffect(() => {
    if (id) {
      const customer = customers.find(c => c.id === parseInt(id));
      if (customer) {
        setSelectedCustomer(customer);
        setDetailModalVisible(true);
      }
    }
  }, [id, customers]);

  const getLevelColor = (level: string) => {
    const colorMap: Record<string, string> = {
      STANDARD: 'default',
      PREMIUM: 'blue',
      VIP: 'gold',
    };
    return colorMap[level] || 'default';
  };

  const getLevelText = (level: string) => {
    const textMap: Record<string, string> = {
      STANDARD: '标准客户',
      PREMIUM: '优质客户',
      VIP: 'VIP客户',
    };
    return textMap[level] || level;
  };

  const columns: ColumnsType<Customer> = [
    {
      title: '客户信息',
      key: 'info',
      render: (_, record) => (
        <Space>
          <Avatar size="large" icon={<UserOutlined />} />
          <div>
            <div>
              <a onClick={() => handleViewDetail(record)}>{record.name}</a>
            </div>
            <Text type="secondary">{record.email}</Text>
          </div>
        </Space>
      ),
    },
    {
      title: '公司',
      dataIndex: 'company',
      key: 'company',
      ellipsis: true,
    },
    {
      title: '行业',
      dataIndex: 'industry',
      key: 'industry',
    },
    {
      title: '客户等级',
      dataIndex: 'level',
      key: 'level',
      render: (level) => (
        <Tag color={getLevelColor(level)}>
          {getLevelText(level)}
        </Tag>
      ),
    },
    {
      title: '联系方式',
      key: 'contact',
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <Text type="secondary">
            <PhoneOutlined /> {record.phone}
          </Text>
          <Text type="secondary">
            <MailOutlined /> {record.email}
          </Text>
        </Space>
      ),
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      render: (tags) => (
        <Space wrap>
          {tags.slice(0, 2).map((tag: string) => (
            <Tag key={tag} size="small">{tag}</Tag>
          ))}
          {tags.length > 2 && (
            <Tag size="small">+{tags.length - 2}</Tag>
          )}
        </Space>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => date.split('T')[0],
      sorter: (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="查看详情">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除这个客户吗？"
              onConfirm={() => handleDelete(record)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="link"
                danger
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  const handleViewDetail = (customer: Customer) => {
    setSelectedCustomer(customer);
    setDetailModalVisible(true);
    navigate(`/admin/customers/${customer.id}`);
  };

  const handleEdit = (customer: Customer) => {
    setEditingCustomer(customer);
    form.setFieldsValue(customer);
    setModalVisible(true);
  };

  const handleAdd = () => {
    setEditingCustomer(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleDelete = async (customer: Customer) => {
    try {
      setLoading(true);
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      setCustomers(customers.filter(c => c.id !== customer.id));
      message.success('客户删除成功！');
    } catch (error) {
      message.error('删除失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);

      if (editingCustomer) {
        // 更新客户
        const updatedCustomers = customers.map(c =>
          c.id === editingCustomer.id
            ? { ...c, ...values, updatedAt: new Date().toISOString() }
            : c
        );
        setCustomers(updatedCustomers);
        message.success('客户信息更新成功！');
      } else {
        // 创建新客户
        const newCustomer: Customer = {
          id: Date.now(),
          ...values,
          tags: values.tags || [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        setCustomers([newCustomer, ...customers]);
        message.success('客户创建成功！');
      }

      setModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error('操作失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleImport = () => {
    message.info('批量导入功能开发中...');
  };

  const handleExport = () => {
    message.info('正在导出客户数据...');
    // 模拟导出
    setTimeout(() => {
      message.success('客户数据导出成功！');
    }, 2000);
  };

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = !searchText ||
      customer.name.toLowerCase().includes(searchText.toLowerCase()) ||
      customer.email.toLowerCase().includes(searchText.toLowerCase()) ||
      customer.company?.toLowerCase().includes(searchText.toLowerCase());
    const matchesIndustry = !industryFilter || customer.industry === industryFilter;
    const matchesLevel = !levelFilter || customer.level === levelFilter;
    return matchesSearch && matchesIndustry && matchesLevel;
  });

  // 获取客户相关的项目和需求数据
  const getCustomerProjects = (customerId: number) => {
    return mockProjects.filter(p => p.proposal?.requirement?.customerId === customerId);
  };

  const getCustomerRequirements = (customerId: number) => {
    return mockRequirements.filter(r => r.customerId === customerId);
  };

  return (
    <div className="page-container">
      <div className="page-header">
        <Title level={3} className="page-title">客户管理</Title>
        <Text className="page-description">
          管理客户信息，跟踪客户项目和需求
        </Text>
      </div>

      <Card>
        <div className="table-header">
          <Space>
            <Input
              placeholder="搜索客户姓名、邮箱或公司"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 250 }}
            />
            <Select
              placeholder="筛选行业"
              value={industryFilter}
              onChange={setIndustryFilter}
              allowClear
              style={{ width: 120 }}
            >
              <Option value="科技">科技</Option>
              <Option value="设计">设计</Option>
              <Option value="金融">金融</Option>
              <Option value="制造">制造</Option>
              <Option value="教育">教育</Option>
            </Select>
            <Select
              placeholder="筛选等级"
              value={levelFilter}
              onChange={setLevelFilter}
              allowClear
              style={{ width: 120 }}
            >
              <Option value="STANDARD">标准客户</Option>
              <Option value="PREMIUM">优质客户</Option>
              <Option value="VIP">VIP客户</Option>
            </Select>
          </Space>

          <Space>
            <Button
              icon={<UploadOutlined />}
              onClick={handleImport}
            >
              批量导入
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={handleExport}
            >
              导出数据
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新增客户
            </Button>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={filteredCustomers}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredCustomers.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 客户详情模态框 */}
      <Modal
        title="客户详情"
        open={detailModalVisible}
        onCancel={() => {
          setDetailModalVisible(false);
          setSelectedCustomer(null);
          navigate('/admin/customers');
        }}
        footer={null}
        width={1200}
        destroyOnClose
      >
        {selectedCustomer && (
          <Tabs defaultActiveKey="info">
            <TabPane tab="基本信息" key="info">
              <Row gutter={[24, 24]}>
                <Col span={16}>
                  <Card title="客户信息" size="small">
                    <Descriptions column={2} size="small">
                      <Descriptions.Item label="客户姓名">
                        {selectedCustomer.name}
                      </Descriptions.Item>
                      <Descriptions.Item label="客户等级">
                        <Tag color={getLevelColor(selectedCustomer.level)}>
                          {getLevelText(selectedCustomer.level)}
                        </Tag>
                      </Descriptions.Item>
                      <Descriptions.Item label="邮箱">
                        {selectedCustomer.email}
                      </Descriptions.Item>
                      <Descriptions.Item label="手机">
                        {selectedCustomer.phone}
                      </Descriptions.Item>
                      <Descriptions.Item label="公司">
                        {selectedCustomer.company}
                      </Descriptions.Item>
                      <Descriptions.Item label="行业">
                        {selectedCustomer.industry}
                      </Descriptions.Item>
                      <Descriptions.Item label="地址" span={2}>
                        {selectedCustomer.address}
                      </Descriptions.Item>
                      <Descriptions.Item label="创建时间">
                        {selectedCustomer.createdAt.split('T')[0]}
                      </Descriptions.Item>
                      <Descriptions.Item label="更新时间">
                        {selectedCustomer.updatedAt.split('T')[0]}
                      </Descriptions.Item>
                    </Descriptions>

                    {selectedCustomer.tags.length > 0 && (
                      <>
                        <div style={{ marginTop: 16 }}>
                          <Text strong>客户标签：</Text>
                          <div style={{ marginTop: 8 }}>
                            <Space wrap>
                              {selectedCustomer.tags.map(tag => (
                                <Tag key={tag}>{tag}</Tag>
                              ))}
                            </Space>
                          </div>
                        </div>
                      </>
                    )}
                  </Card>
                </Col>

                <Col span={8}>
                  <Card title="客户统计" size="small">
                    <Row gutter={[16, 16]}>
                      <Col span={12}>
                        <Statistic
                          title="总需求"
                          value={getCustomerRequirements(selectedCustomer.id).length}
                          suffix="个"
                        />
                      </Col>
                      <Col span={12}>
                        <Statistic
                          title="总项目"
                          value={getCustomerProjects(selectedCustomer.id).length}
                          suffix="个"
                        />
                      </Col>
                      <Col span={12}>
                        <Statistic
                          title="完成项目"
                          value={getCustomerProjects(selectedCustomer.id).filter(p => p.status === 'COMPLETED').length}
                          suffix="个"
                        />
                      </Col>
                      <Col span={12}>
                        <Statistic
                          title="满意度"
                          value={4.5}
                          precision={1}
                          suffix="/ 5.0"
                        />
                      </Col>
                    </Row>
                  </Card>

                  <Card title="快速操作" size="small" style={{ marginTop: 16 }}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Button
                        icon={<EditOutlined />}
                        block
                        onClick={() => handleEdit(selectedCustomer)}
                      >
                        编辑客户信息
                      </Button>
                      <Button
                        icon={<FileTextOutlined />}
                        block
                        onClick={() => navigate(`/admin/requirements?customerId=${selectedCustomer.id}`)}
                      >
                        查看需求
                      </Button>
                      <Button
                        icon={<ProjectOutlined />}
                        block
                        onClick={() => navigate(`/admin/projects?customerId=${selectedCustomer.id}`)}
                      >
                        查看项目
                      </Button>
                    </Space>
                  </Card>
                </Col>
              </Row>
            </TabPane>

            <TabPane tab="需求历史" key="requirements">
              <List
                dataSource={getCustomerRequirements(selectedCustomer.id)}
                renderItem={(requirement) => (
                  <List.Item
                    actions={[
                      <Tag color="blue">{requirement.status}</Tag>,
                      <Button type="link">查看详情</Button>
                    ]}
                  >
                    <List.Item.Meta
                      title={requirement.title}
                      description={
                        <Space direction="vertical" size={4}>
                          <Text type="secondary">{requirement.description}</Text>
                          <Text type="secondary">
                            创建时间：{requirement.createdAt.split('T')[0]}
                          </Text>
                        </Space>
                      }
                    />
                  </List.Item>
                )}
              />
            </TabPane>

            <TabPane tab="项目历史" key="projects">
              <List
                dataSource={getCustomerProjects(selectedCustomer.id)}
                renderItem={(project) => (
                  <List.Item
                    actions={[
                      <Progress percent={project.progressPercentage} size="small" />,
                      <Tag color="green">{project.status}</Tag>,
                      <Button type="link">查看详情</Button>
                    ]}
                  >
                    <List.Item.Meta
                      title={project.name}
                      description={
                        <Space direction="vertical" size={4}>
                          <Text type="secondary">{project.description}</Text>
                          <Text type="secondary">
                            开始时间：{project.startDate} - 预计完成：{project.endDate}
                          </Text>
                        </Space>
                      }
                    />
                  </List.Item>
                )}
              />
            </TabPane>
          </Tabs>
        )}
      </Modal>

      {/* 新增/编辑客户模态框 */}
      <Modal
        title={editingCustomer ? '编辑客户' : '新增客户'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingCustomer(null);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        confirmLoading={loading}
        okText={editingCustomer ? '更新' : '创建'}
        cancelText="取消"
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="客户姓名"
                rules={[{ required: true, message: '请输入客户姓名' }]}
              >
                <Input prefix={<UserOutlined />} placeholder="请输入客户姓名" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { required: true, message: '请输入邮箱' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input prefix={<MailOutlined />} placeholder="请输入邮箱" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="手机号码"
                rules={[
                  { required: true, message: '请输入手机号码' },
                  { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' }
                ]}
              >
                <Input prefix={<PhoneOutlined />} placeholder="请输入手机号码" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="level"
                label="客户等级"
                rules={[{ required: true, message: '请选择客户等级' }]}
              >
                <Select placeholder="请选择客户等级">
                  <Option value="STANDARD">标准客户</Option>
                  <Option value="PREMIUM">优质客户</Option>
                  <Option value="VIP">VIP客户</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="company"
                label="公司名称"
              >
                <Input prefix={<HomeOutlined />} placeholder="请输入公司名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="industry"
                label="所属行业"
              >
                <Select placeholder="请选择所属行业">
                  <Option value="科技">科技</Option>
                  <Option value="设计">设计</Option>
                  <Option value="金融">金融</Option>
                  <Option value="制造">制造</Option>
                  <Option value="教育">教育</Option>
                  <Option value="医疗">医疗</Option>
                  <Option value="零售">零售</Option>
                  <Option value="其他">其他</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="address"
            label="联系地址"
          >
            <TextArea rows={3} placeholder="请输入详细地址" />
          </Form.Item>

          <Form.Item
            name="tags"
            label="客户标签"
          >
            <Select
              mode="tags"
              placeholder="请输入或选择客户标签"
              style={{ width: '100%' }}
            >
              <Option value="高端客户">高端客户</Option>
              <Option value="回头客">回头客</Option>
              <Option value="推荐客户">推荐客户</Option>
              <Option value="大单客户">大单客户</Option>
              <Option value="长期合作">长期合作</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Customers;
