import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Input,
  Select,
  Modal,
  Form,
  Row,
  Col,
  Descriptions,
  Avatar,
  Tabs,
  List,
  Progress,
  Timeline,
  Alert,
  Upload,
  message,
  Tooltip,
  Popconfirm,
  Badge,
  Rate,
} from 'antd';
import {
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  UserAddOutlined,
  CheckOutlined,
  CloseOutlined,
  FileTextOutlined,
  TagOutlined,
  ClockCircleOutlined,
  UserOutlined,
  DownloadOutlined,
  MessageOutlined,
  BulbOutlined,
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import { useAppSelector } from '@/store';
import { mockRequirements, mockCustomers, mockUsers } from '@/data/mockData';
import { STATUS_COLORS } from '@/constants';
import type { Requirement, RequirementTag } from '@/types';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

const Requirements: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [form] = Form.useForm();

  const [requirements, setRequirements] = useState<Requirement[]>(mockRequirements);
  const [loading, setLoading] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [assignModalVisible, setAssignModalVisible] = useState(false);
  const [analysisModalVisible, setAnalysisModalVisible] = useState(false);
  const [selectedRequirement, setSelectedRequirement] = useState<Requirement | null>(null);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [categoryFilter, setCategoryFilter] = useState<string>('');
  const [priorityFilter, setPriorityFilter] = useState<string>('');
  const [assignedFilter, setAssignedFilter] = useState<string>('');

  const { user } = useAppSelector((state) => state.auth);

  useEffect(() => {
    if (id) {
      const requirement = requirements.find(r => r.id === parseInt(id));
      if (requirement) {
        setSelectedRequirement(requirement);
        setDetailModalVisible(true);
      }
    }
  }, [id, requirements]);

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      SUBMITTED: '已提交',
      ANALYZING: '分析中',
      ASSIGNED: '已分配',
      IN_PROGRESS: '进行中',
      COMPLETED: '已完成',
    };
    return statusMap[status] || status;
  };

  const getPriorityText = (priority: string) => {
    const priorityMap: Record<string, string> = {
      LOW: '低',
      MEDIUM: '中',
      HIGH: '高',
      URGENT: '紧急',
    };
    return priorityMap[priority] || priority;
  };

  const getCategoryText = (category: string) => {
    const categoryMap: Record<string, string> = {
      LIVING_ROOM: '客厅家具',
      BEDROOM: '卧室家具',
      KITCHEN: '厨房家具',
      DINING_ROOM: '餐厅家具',
      OFFICE: '办公家具',
      BATHROOM: '浴室家具',
      OUTDOOR: '户外家具',
      CUSTOM: '定制家具',
    };
    return categoryMap[category] || category;
  };

  const columns: ColumnsType<Requirement> = [
    {
      title: '需求信息',
      key: 'info',
      render: (_, record) => (
        <div>
          <div>
            <a onClick={() => handleViewDetail(record)}>{record.title}</a>
          </div>
          <Text type="secondary" ellipsis>
            {record.description}
          </Text>
        </div>
      ),
      width: 300,
    },
    {
      title: '客户',
      key: 'customer',
      render: (_, record) => (
        <Space>
          <Avatar size="small" icon={<UserOutlined />} />
          <div>
            <div>{record.customer?.name}</div>
            <Text type="secondary" style={{ fontSize: 12 }}>
              {record.customer?.company}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
      render: (category) => getCategoryText(category),
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority) => {
        const colorMap: Record<string, string> = {
          LOW: 'green',
          MEDIUM: 'orange',
          HIGH: 'red',
          URGENT: 'red',
        };
        return (
          <Tag color={colorMap[priority]} style={{
            fontWeight: priority === 'URGENT' ? 'bold' : 'normal'
          }}>
            {getPriorityText(priority)}
          </Tag>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={STATUS_COLORS[status as keyof typeof STATUS_COLORS]}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '负责人',
      key: 'assignedUser',
      render: (_, record) => (
        record.assignedUser ? (
          <Space>
            <Avatar size="small" src={record.assignedUser.avatar}>
              {record.assignedUser.fullName[0]}
            </Avatar>
            <Text>{record.assignedUser.fullName}</Text>
          </Space>
        ) : (
          <Text type="secondary">未分配</Text>
        )
      ),
    },
    {
      title: '期望交付',
      dataIndex: 'expectedDelivery',
      key: 'expectedDelivery',
      render: (date) => {
        const isOverdue = new Date(date) < new Date();
        return (
          <Text type={isOverdue ? 'danger' : 'secondary'}>
            {date}
          </Text>
        );
      },
      sorter: (a, b) => new Date(a.expectedDelivery).getTime() - new Date(b.expectedDelivery).getTime(),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => date.split('T')[0],
      sorter: (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="查看详情">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          {record.status === 'SUBMITTED' && (
            <Tooltip title="分配负责人">
              <Button
                type="link"
                icon={<UserAddOutlined />}
                onClick={() => handleAssign(record)}
              />
            </Tooltip>
          )}
          {record.status === 'ASSIGNED' && (
            <Tooltip title="开始分析">
              <Button
                type="link"
                icon={<BulbOutlined />}
                style={{ color: '#52c41a' }}
                onClick={() => handleStartAnalysis(record)}
              />
            </Tooltip>
          )}
          {['ANALYZING', 'IN_PROGRESS'].includes(record.status) && (
            <Tooltip title="标记完成">
              <Button
                type="link"
                icon={<CheckOutlined />}
                style={{ color: '#52c41a' }}
                onClick={() => handleComplete(record)}
              />
            </Tooltip>
          )}
          <Tooltip title="需求分析">
            <Button
              type="link"
              icon={<FileTextOutlined />}
              onClick={() => handleAnalysis(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const handleViewDetail = (requirement: Requirement) => {
    setSelectedRequirement(requirement);
    setDetailModalVisible(true);
    navigate(`/admin/requirements/${requirement.id}`);
  };

  const handleAssign = (requirement: Requirement) => {
    setSelectedRequirement(requirement);
    setAssignModalVisible(true);
  };

  const handleStartAnalysis = async (requirement: Requirement) => {
    try {
      setLoading(true);
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      const updatedRequirements = requirements.map(r =>
        r.id === requirement.id
          ? { ...r, status: 'ANALYZING' as const, updatedAt: new Date().toISOString() }
          : r
      );
      setRequirements(updatedRequirements);
      message.success('需求分析已开始！');
    } catch (error) {
      message.error('操作失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleComplete = async (requirement: Requirement) => {
    Modal.confirm({
      title: '确认完成需求',
      content: '确定要标记此需求为已完成吗？',
      onOk: async () => {
        try {
          setLoading(true);
          await new Promise(resolve => setTimeout(resolve, 1000));

          const updatedRequirements = requirements.map(r =>
            r.id === requirement.id
              ? { ...r, status: 'COMPLETED' as const, updatedAt: new Date().toISOString() }
              : r
          );
          setRequirements(updatedRequirements);
          message.success('需求已标记为完成！');
        } catch (error) {
          message.error('操作失败，请重试');
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const handleAnalysis = (requirement: Requirement) => {
    setSelectedRequirement(requirement);
    setAnalysisModalVisible(true);
  };

  const handleAssignSubmit = async (values: any) => {
    try {
      setLoading(true);

      const assignedUser = mockUsers.find(u => u.id === values.assignedTo);
      const updatedRequirements = requirements.map(r =>
        r.id === selectedRequirement!.id
          ? {
              ...r,
              status: 'ASSIGNED' as const,
              assignedTo: values.assignedTo,
              assignedUser,
              updatedAt: new Date().toISOString()
            }
          : r
      );

      setRequirements(updatedRequirements);
      setAssignModalVisible(false);
      message.success('需求分配成功！');
    } catch (error) {
      message.error('分配失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const filteredRequirements = requirements.filter(requirement => {
    const matchesSearch = !searchText ||
      requirement.title.toLowerCase().includes(searchText.toLowerCase()) ||
      requirement.description.toLowerCase().includes(searchText.toLowerCase()) ||
      requirement.customer?.name.toLowerCase().includes(searchText.toLowerCase());
    const matchesStatus = !statusFilter || requirement.status === statusFilter;
    const matchesCategory = !categoryFilter || requirement.category === categoryFilter;
    const matchesPriority = !priorityFilter || requirement.priority === priorityFilter;
    const matchesAssigned = !assignedFilter ||
      (assignedFilter === 'unassigned' ? !requirement.assignedTo : requirement.assignedTo?.toString() === assignedFilter);

    return matchesSearch && matchesStatus && matchesCategory && matchesPriority && matchesAssigned;
  });

  return (
    <div className="page-container">
      <div className="page-header">
        <Title level={3} className="page-title">需求管理</Title>
        <Text className="page-description">
          管理客户需求，分配处理人员，跟踪需求处理进度
        </Text>
      </div>

      <Card>
        <div className="table-header">
          <Space wrap>
            <Input
              placeholder="搜索需求标题、描述或客户"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 250 }}
            />
            <Select
              placeholder="筛选状态"
              value={statusFilter}
              onChange={setStatusFilter}
              allowClear
              style={{ width: 120 }}
            >
              <Option value="SUBMITTED">已提交</Option>
              <Option value="ANALYZING">分析中</Option>
              <Option value="ASSIGNED">已分配</Option>
              <Option value="IN_PROGRESS">进行中</Option>
              <Option value="COMPLETED">已完成</Option>
            </Select>
            <Select
              placeholder="筛选类别"
              value={categoryFilter}
              onChange={setCategoryFilter}
              allowClear
              style={{ width: 120 }}
            >
              <Option value="LIVING_ROOM">客厅家具</Option>
              <Option value="BEDROOM">卧室家具</Option>
              <Option value="KITCHEN">厨房家具</Option>
              <Option value="DINING_ROOM">餐厅家具</Option>
              <Option value="OFFICE">办公家具</Option>
            </Select>
            <Select
              placeholder="筛选优先级"
              value={priorityFilter}
              onChange={setPriorityFilter}
              allowClear
              style={{ width: 120 }}
            >
              <Option value="LOW">低</Option>
              <Option value="MEDIUM">中</Option>
              <Option value="HIGH">高</Option>
              <Option value="URGENT">紧急</Option>
            </Select>
            <Select
              placeholder="筛选负责人"
              value={assignedFilter}
              onChange={setAssignedFilter}
              allowClear
              style={{ width: 120 }}
            >
              <Option value="unassigned">未分配</Option>
              {mockUsers.filter(u => u.roles.some(r => ['DESIGNER', 'SALES'].includes(r.name))).map(user => (
                <Option key={user.id} value={user.id.toString()}>
                  {user.fullName}
                </Option>
              ))}
            </Select>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={filteredRequirements}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredRequirements.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 需求详情模态框 */}
      <Modal
        title="需求详情"
        open={detailModalVisible}
        onCancel={() => {
          setDetailModalVisible(false);
          setSelectedRequirement(null);
          navigate('/admin/requirements');
        }}
        footer={null}
        width={1200}
        destroyOnClose
      >
        {selectedRequirement && (
          <Tabs defaultActiveKey="info">
            <TabPane tab="基本信息" key="info">
              <Row gutter={[24, 24]}>
                <Col span={16}>
                  <Card title="需求信息" size="small">
                    <Descriptions column={2} size="small">
                      <Descriptions.Item label="需求标题">
                        {selectedRequirement.title}
                      </Descriptions.Item>
                      <Descriptions.Item label="状态">
                        <Tag color={STATUS_COLORS[selectedRequirement.status as keyof typeof STATUS_COLORS]}>
                          {getStatusText(selectedRequirement.status)}
                        </Tag>
                      </Descriptions.Item>
                      <Descriptions.Item label="客户">
                        {selectedRequirement.customer?.name}
                      </Descriptions.Item>
                      <Descriptions.Item label="公司">
                        {selectedRequirement.customer?.company}
                      </Descriptions.Item>
                      <Descriptions.Item label="类别">
                        {getCategoryText(selectedRequirement.category)}
                      </Descriptions.Item>
                      <Descriptions.Item label="优先级">
                        <Tag color={
                          selectedRequirement.priority === 'HIGH' || selectedRequirement.priority === 'URGENT' ? 'red' :
                          selectedRequirement.priority === 'MEDIUM' ? 'orange' : 'green'
                        }>
                          {getPriorityText(selectedRequirement.priority)}
                        </Tag>
                      </Descriptions.Item>
                      <Descriptions.Item label="预算范围">
                        {selectedRequirement.budgetRange}
                      </Descriptions.Item>
                      <Descriptions.Item label="期望交付">
                        {selectedRequirement.expectedDelivery}
                      </Descriptions.Item>
                      <Descriptions.Item label="负责人">
                        {selectedRequirement.assignedUser ? (
                          <Space>
                            <Avatar size="small" src={selectedRequirement.assignedUser.avatar}>
                              {selectedRequirement.assignedUser.fullName[0]}
                            </Avatar>
                            {selectedRequirement.assignedUser.fullName}
                          </Space>
                        ) : (
                          <Text type="secondary">未分配</Text>
                        )}
                      </Descriptions.Item>
                      <Descriptions.Item label="创建时间">
                        {selectedRequirement.createdAt.split('T')[0]}
                      </Descriptions.Item>
                    </Descriptions>

                    <div style={{ marginTop: 16 }}>
                      <Title level={5}>需求描述</Title>
                      <Paragraph>{selectedRequirement.description}</Paragraph>
                    </div>

                    {selectedRequirement.tags && selectedRequirement.tags.length > 0 && (
                      <div style={{ marginTop: 16 }}>
                        <Title level={5}>智能标签</Title>
                        <Space wrap>
                          {selectedRequirement.tags.map((tag, index) => (
                            <Tag key={index} color="blue">
                              <Space>
                                <TagOutlined />
                                {tag.tagName}: {tag.tagValue}
                                <Text type="secondary">({(tag.confidenceScore * 100).toFixed(0)}%)</Text>
                              </Space>
                            </Tag>
                          ))}
                        </Space>
                      </div>
                    )}
                  </Card>
                </Col>

                <Col span={8}>
                  <Card title="处理进度" size="small">
                    <Timeline
                      items={[
                        {
                          color: 'green',
                          children: '客户提交需求',
                          dot: <CheckOutlined />,
                        },
                        {
                          color: selectedRequirement.status === 'SUBMITTED' ? 'gray' : 'green',
                          children: '分配处理人员',
                          dot: selectedRequirement.assignedTo ? <CheckOutlined /> : <ClockCircleOutlined />,
                        },
                        {
                          color: ['ANALYZING', 'IN_PROGRESS', 'COMPLETED'].includes(selectedRequirement.status) ? 'green' : 'gray',
                          children: '需求分析',
                          dot: ['ANALYZING', 'IN_PROGRESS', 'COMPLETED'].includes(selectedRequirement.status) ? <CheckOutlined /> : <ClockCircleOutlined />,
                        },
                        {
                          color: ['IN_PROGRESS', 'COMPLETED'].includes(selectedRequirement.status) ? 'green' : 'gray',
                          children: '方案设计',
                          dot: ['IN_PROGRESS', 'COMPLETED'].includes(selectedRequirement.status) ? <CheckOutlined /> : <ClockCircleOutlined />,
                        },
                        {
                          color: selectedRequirement.status === 'COMPLETED' ? 'green' : 'gray',
                          children: '需求完成',
                          dot: selectedRequirement.status === 'COMPLETED' ? <CheckOutlined /> : <ClockCircleOutlined />,
                        },
                      ]}
                    />
                  </Card>

                  <Card title="快速操作" size="small" style={{ marginTop: 16 }}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      {selectedRequirement.status === 'SUBMITTED' && (
                        <Button
                          type="primary"
                          icon={<UserAddOutlined />}
                          block
                          onClick={() => handleAssign(selectedRequirement)}
                        >
                          分配负责人
                        </Button>
                      )}
                      {selectedRequirement.status === 'ASSIGNED' && (
                        <Button
                          type="primary"
                          icon={<BulbOutlined />}
                          block
                          onClick={() => handleStartAnalysis(selectedRequirement)}
                        >
                          开始分析
                        </Button>
                      )}
                      {['ANALYZING', 'IN_PROGRESS'].includes(selectedRequirement.status) && (
                        <Button
                          type="primary"
                          icon={<CheckOutlined />}
                          block
                          onClick={() => handleComplete(selectedRequirement)}
                        >
                          标记完成
                        </Button>
                      )}
                      <Button
                        icon={<FileTextOutlined />}
                        block
                        onClick={() => handleAnalysis(selectedRequirement)}
                      >
                        需求分析
                      </Button>
                      <Button
                        icon={<MessageOutlined />}
                        block
                        onClick={() => message.info('沟通功能开发中...')}
                      >
                        联系客户
                      </Button>
                    </Space>
                  </Card>
                </Col>
              </Row>
            </TabPane>

            <TabPane tab="附件文件" key="attachments">
              <Card>
                {selectedRequirement.attachments && selectedRequirement.attachments.length > 0 ? (
                  <List
                    dataSource={selectedRequirement.attachments}
                    renderItem={(attachment) => (
                      <List.Item
                        actions={[
                          <Button type="link" icon={<DownloadOutlined />}>
                            下载
                          </Button>
                        ]}
                      >
                        <List.Item.Meta
                          title={attachment.fileName}
                          description={`文件大小: ${(attachment.fileSize / 1024 / 1024).toFixed(2)} MB`}
                        />
                      </List.Item>
                    )}
                  />
                ) : (
                  <div style={{ textAlign: 'center', padding: '40px 0' }}>
                    <Text type="secondary">暂无附件</Text>
                  </div>
                )}
              </Card>
            </TabPane>

            <TabPane tab="处理记录" key="history">
              <Card>
                <Timeline>
                  <Timeline.Item color="green">
                    <Text strong>需求提交</Text>
                    <br />
                    <Text type="secondary">
                      {selectedRequirement.customer?.name} 提交了需求
                    </Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      {selectedRequirement.createdAt.split('T')[0]}
                    </Text>
                  </Timeline.Item>

                  {selectedRequirement.assignedUser && (
                    <Timeline.Item color="blue">
                      <Text strong>分配处理人</Text>
                      <br />
                      <Text type="secondary">
                        分配给 {selectedRequirement.assignedUser.fullName}
                      </Text>
                    </Timeline.Item>
                  )}

                  {['ANALYZING', 'IN_PROGRESS', 'COMPLETED'].includes(selectedRequirement.status) && (
                    <Timeline.Item color="orange">
                      <Text strong>开始分析</Text>
                      <br />
                      <Text type="secondary">
                        {selectedRequirement.assignedUser?.fullName} 开始分析需求
                      </Text>
                    </Timeline.Item>
                  )}

                  {selectedRequirement.status === 'COMPLETED' && (
                    <Timeline.Item color="green">
                      <Text strong>需求完成</Text>
                      <br />
                      <Text type="secondary">
                        需求处理完成，已生成方案
                      </Text>
                    </Timeline.Item>
                  )}
                </Timeline>
              </Card>
            </TabPane>
          </Tabs>
        )}
      </Modal>

      {/* 分配负责人模态框 */}
      <Modal
        title="分配负责人"
        open={assignModalVisible}
        onCancel={() => {
          setAssignModalVisible(false);
          setSelectedRequirement(null);
        }}
        onOk={() => form.submit()}
        confirmLoading={loading}
        okText="确认分配"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleAssignSubmit}
        >
          <Alert
            message="分配说明"
            description="请根据需求类型和复杂程度选择合适的处理人员。分配后系统会自动通知相关人员。"
            type="info"
            showIcon
            style={{ marginBottom: 24 }}
          />

          <Form.Item
            name="assignedTo"
            label="选择负责人"
            rules={[{ required: true, message: '请选择负责人' }]}
          >
            <Select placeholder="请选择负责人" size="large">
              {mockUsers
                .filter(u => u.roles.some(r => ['DESIGNER', 'SALES'].includes(r.name)))
                .map(user => (
                  <Option key={user.id} value={user.id}>
                    <Space>
                      <Avatar size="small" src={user.avatar}>
                        {user.fullName[0]}
                      </Avatar>
                      <div>
                        <div>{user.fullName}</div>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {user.department} - {user.position}
                        </Text>
                      </div>
                    </Space>
                  </Option>
                ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="note"
            label="备注说明"
          >
            <TextArea
              rows={3}
              placeholder="可以添加一些特殊说明或要求..."
              maxLength={200}
              showCount
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 需求分析模态框 */}
      <Modal
        title="需求分析"
        open={analysisModalVisible}
        onCancel={() => {
          setAnalysisModalVisible(false);
          setSelectedRequirement(null);
        }}
        footer={null}
        width={800}
        destroyOnClose
      >
        {selectedRequirement && (
          <div>
            <Card title="智能分析结果" size="small" style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Statistic
                    title="复杂度评分"
                    value={8.5}
                    precision={1}
                    suffix="/ 10"
                    valueStyle={{ color: '#fa8c16' }}
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="预估工期"
                    value={25}
                    suffix="天"
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="预估成本"
                    value={75000}
                    prefix="¥"
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="匹配度"
                    value={92}
                    suffix="%"
                    valueStyle={{ color: '#722ed1' }}
                  />
                </Col>
              </Row>
            </Card>

            <Card title="关键词分析" size="small" style={{ marginBottom: 16 }}>
              <Space wrap>
                {selectedRequirement.tags?.map((tag, index) => (
                  <Tag key={index} color="blue">
                    {tag.tagName}: {tag.tagValue}
                    <Badge
                      count={`${(tag.confidenceScore * 100).toFixed(0)}%`}
                      style={{ backgroundColor: '#52c41a', marginLeft: 8 }}
                    />
                  </Tag>
                ))}
              </Space>
            </Card>

            <Card title="相似需求" size="small" style={{ marginBottom: 16 }}>
              <List
                size="small"
                dataSource={[
                  {
                    id: 1,
                    title: '现代简约风格客厅设计',
                    similarity: 85,
                    customer: '李女士',
                    status: 'COMPLETED'
                  },
                  {
                    id: 2,
                    title: '北欧风格家具定制',
                    similarity: 78,
                    customer: '张先生',
                    status: 'IN_PROGRESS'
                  }
                ]}
                renderItem={(item) => (
                  <List.Item
                    actions={[
                      <Button type="link" size="small">查看</Button>
                    ]}
                  >
                    <List.Item.Meta
                      title={
                        <Space>
                          <Text>{item.title}</Text>
                          <Tag color="green">{item.similarity}% 相似</Tag>
                        </Space>
                      }
                      description={`客户：${item.customer} | 状态：${item.status}`}
                    />
                  </List.Item>
                )}
              />
            </Card>

            <Card title="建议方案" size="small">
              <List
                dataSource={[
                  {
                    title: '方案A：经典现代风格',
                    description: '采用简约线条设计，注重功能性与美观性的平衡',
                    score: 9.2
                  },
                  {
                    title: '方案B：轻奢现代风格',
                    description: '在现代简约基础上增加轻奢元素，提升品质感',
                    score: 8.8
                  },
                  {
                    title: '方案C：北欧现代风格',
                    description: '融合北欧元素，强调自然材质和温馨感',
                    score: 8.5
                  }
                ]}
                renderItem={(item) => (
                  <List.Item
                    actions={[
                      <Rate disabled value={item.score / 2} />,
                      <Button type="link">选择</Button>
                    ]}
                  >
                    <List.Item.Meta
                      title={item.title}
                      description={item.description}
                    />
                  </List.Item>
                )}
              />
            </Card>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Requirements;
