import React from 'react';
import { Row, Col, Card, Statistic, Typography, Table, Tag, Progress } from 'antd';
import {
  UserOutlined,
  FileTextOutlined,
  ProjectOutlined,
  CheckCircleOutlined,
  TrophyOutlined,
  RiseOutlined,
} from '@ant-design/icons';
import { mockDashboardStats, mockChartData, mockRequirements, mockProjects } from '@/data/mockData';
import { STATUS_COLORS } from '@/constants';

const { Title, Text } = Typography;

const AdminDashboard: React.FC = () => {
  const stats = mockDashboardStats;

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      SUBMITTED: '已提交',
      ANALYZING: '分析中',
      ASSIGNED: '已分配',
      IN_PROGRESS: '进行中',
      COMPLETED: '已完成',
      PLANNING: '规划中',
      DELAYED: '已延期',
      CANCELLED: '已取消',
    };
    return statusMap[status] || status;
  };

  const recentRequirements = mockRequirements.slice(0, 5);
  const activeProjects = mockProjects.slice(0, 5);

  const requirementColumns = [
    {
      title: '需求标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
    },
    {
      title: '客户',
      dataIndex: ['customer', 'name'],
      key: 'customer',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={STATUS_COLORS[status as keyof typeof STATUS_COLORS]}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => date.split('T')[0],
    },
  ];

  const projectColumns = [
    {
      title: '项目名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
    {
      title: '进度',
      dataIndex: 'progressPercentage',
      key: 'progress',
      render: (progress: number, record: any) => (
        <Progress
          percent={progress}
          size="small"
          status={record.status === 'DELAYED' ? 'exception' : 'active'}
        />
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={STATUS_COLORS[status as keyof typeof STATUS_COLORS]}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '预计完成',
      dataIndex: 'endDate',
      key: 'endDate',
    },
  ];

  return (
    <div className="page-container">
      <div className="page-header">
        <Title level={3} className="page-title">管理工作台</Title>
        <Text className="page-description">
          总览业务数据和关键指标
        </Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总客户数"
              value={stats.totalCustomers}
              prefix={<UserOutlined style={{ color: '#1890ff' }} />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="活跃项目"
              value={stats.activeProjects}
              prefix={<ProjectOutlined style={{ color: '#52c41a' }} />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="本月营收"
              value={stats.monthlyRevenue}
              prefix={<RiseOutlined style={{ color: '#fa8c16' }} />}
              suffix="元"
              precision={0}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="平均NPS"
              value={stats.averageNPS}
              prefix={<TrophyOutlined style={{ color: '#722ed1' }} />}
              precision={1}
            />
          </Card>
        </Col>
      </Row>

      {/* 关键指标 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="已完成项目"
              value={stats.completedProjects}
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="客户满意度"
              value={stats.customerSatisfaction}
              prefix="⭐"
              precision={1}
              suffix="/ 5.0"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="准时交付率"
              value={stats.onTimeDeliveryRate * 100}
              suffix="%"
              precision={1}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="待处理需求"
              value={stats.pendingRequirements}
              prefix={<FileTextOutlined style={{ color: '#fa8c16' }} />}
              suffix="个"
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 最新需求 */}
        <Col xs={24} lg={12}>
          <Card title="最新需求" size="small">
            <Table
              columns={requirementColumns}
              dataSource={recentRequirements}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>

        {/* 活跃项目 */}
        <Col xs={24} lg={12}>
          <Card title="活跃项目" size="small">
            <Table
              columns={projectColumns}
              dataSource={activeProjects}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default AdminDashboard;
