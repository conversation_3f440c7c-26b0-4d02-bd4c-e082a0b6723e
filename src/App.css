.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 全局样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consol<PERSON>, 'Courier New',
    monospace;
}

/* 布局样式 */
.layout-container {
  min-height: 100vh;
}

.layout-header {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.layout-content {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.layout-sidebar {
  background: #fff;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

/* 页面容器 */
.page-container {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.page-description {
  color: #8c8c8c;
  margin-top: 8px;
  margin-bottom: 0;
}

/* 卡片样式 */
.card-container {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

/* 表格样式 */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.table-header {
  padding: 16px 24px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.table-actions {
  display: flex;
  gap: 8px;
}

/* 表单样式 */
.form-container {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
}

.form-section {
  margin-bottom: 32px;
}

.form-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

/* 状态标签 */
.status-tag {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-submitted { background: #e6f7ff; color: #1890ff; }
.status-analyzing { background: #f6ffed; color: #52c41a; }
.status-assigned { background: #e6f7ff; color: #1890ff; }
.status-in-progress { background: #fff7e6; color: #fa8c16; }
.status-completed { background: #f6ffed; color: #52c41a; }
.status-draft { background: #f5f5f5; color: #8c8c8c; }
.status-reviewing { background: #fff7e6; color: #fa8c16; }
.status-sent { background: #e6f7ff; color: #1890ff; }
.status-confirmed { background: #f6ffed; color: #52c41a; }
.status-rejected { background: #fff2f0; color: #ff4d4f; }
.status-planning { background: #f5f5f5; color: #8c8c8c; }
.status-delayed { background: #fff2f0; color: #ff4d4f; }
.status-cancelled { background: #f5f5f5; color: #8c8c8c; }

/* 优先级标签 */
.priority-low { color: #52c41a; }
.priority-medium { color: #fa8c16; }
.priority-high { color: #ff4d4f; }
.priority-urgent { color: #ff4d4f; font-weight: bold; }

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: #8c8c8c;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  margin-bottom: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-content {
    padding: 16px;
  }
  
  .page-container {
    padding: 16px;
  }
  
  .card-container {
    padding: 16px;
  }
  
  .form-container {
    padding: 16px;
  }
  
  .table-header {
    padding: 12px 16px;
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .form-actions {
    flex-direction: column;
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
