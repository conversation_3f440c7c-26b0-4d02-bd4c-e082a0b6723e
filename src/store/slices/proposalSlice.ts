import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { Proposal, PaginatedResponse, ProposalFormData } from '@/types';
import { proposalService } from '@/services/proposalService';

interface ProposalState {
  proposals: Proposal[];
  currentProposal: Proposal | null;
  totalCount: number;
  currentPage: number;
  pageSize: number;
  loading: boolean;
  error: string | null;
  searchQuery: string;
  filters: {
    status?: string;
    requirementId?: number;
    createdBy?: number;
  };
}

const initialState: ProposalState = {
  proposals: [],
  currentProposal: null,
  totalCount: 0,
  currentPage: 1,
  pageSize: 20,
  loading: false,
  error: null,
  searchQuery: '',
  filters: {},
};

export const fetchProposals = createAsyncThunk(
  'proposal/fetchProposals',
  async (params: any = {}, { rejectWithValue }) => {
    try {
      const response = await proposalService.getProposals(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取方案列表失败');
    }
  }
);

export const fetchProposalById = createAsyncThunk(
  'proposal/fetchProposalById',
  async (id: number, { rejectWithValue }) => {
    try {
      const response = await proposalService.getProposalById(id);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取方案详情失败');
    }
  }
);

export const createProposal = createAsyncThunk(
  'proposal/createProposal',
  async (data: ProposalFormData, { rejectWithValue }) => {
    try {
      const response = await proposalService.createProposal(data);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '创建方案失败');
    }
  }
);

export const updateProposal = createAsyncThunk(
  'proposal/updateProposal',
  async ({ id, data }: { id: number; data: Partial<Proposal> }, { rejectWithValue }) => {
    try {
      const response = await proposalService.updateProposal(id, data);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '更新方案失败');
    }
  }
);

export const submitProposal = createAsyncThunk(
  'proposal/submitProposal',
  async (id: number, { rejectWithValue }) => {
    try {
      const response = await proposalService.submitProposal(id);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '提交方案失败');
    }
  }
);

export const confirmProposal = createAsyncThunk(
  'proposal/confirmProposal',
  async (id: number, { rejectWithValue }) => {
    try {
      const response = await proposalService.confirmProposal(id);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '确认方案失败');
    }
  }
);

const proposalSlice = createSlice({
  name: 'proposal',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setSearchQuery: (state, action) => {
      state.searchQuery = action.payload;
    },
    setFilters: (state, action) => {
      state.filters = action.payload;
    },
    setCurrentPage: (state, action) => {
      state.currentPage = action.payload;
    },
    clearCurrentProposal: (state) => {
      state.currentProposal = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchProposals.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProposals.fulfilled, (state, action) => {
        state.loading = false;
        const response = action.payload as PaginatedResponse<Proposal>;
        state.proposals = response.content;
        state.totalCount = response.totalElements;
        state.currentPage = response.currentPage;
      })
      .addCase(fetchProposals.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchProposalById.fulfilled, (state, action) => {
        state.currentProposal = action.payload;
      })
      .addCase(createProposal.fulfilled, (state, action) => {
        state.proposals.unshift(action.payload);
        state.totalCount += 1;
      })
      .addCase(updateProposal.fulfilled, (state, action) => {
        const index = state.proposals.findIndex(p => p.id === action.payload.id);
        if (index !== -1) {
          state.proposals[index] = action.payload;
        }
        if (state.currentProposal?.id === action.payload.id) {
          state.currentProposal = action.payload;
        }
      })
      .addCase(submitProposal.fulfilled, (state, action) => {
        const index = state.proposals.findIndex(p => p.id === action.payload.id);
        if (index !== -1) {
          state.proposals[index] = action.payload;
        }
        if (state.currentProposal?.id === action.payload.id) {
          state.currentProposal = action.payload;
        }
      })
      .addCase(confirmProposal.fulfilled, (state, action) => {
        const index = state.proposals.findIndex(p => p.id === action.payload.id);
        if (index !== -1) {
          state.proposals[index] = action.payload;
        }
        if (state.currentProposal?.id === action.payload.id) {
          state.currentProposal = action.payload;
        }
      });
  },
});

export const {
  clearError,
  setSearchQuery,
  setFilters,
  setCurrentPage,
  clearCurrentProposal,
} = proposalSlice.actions;

export default proposalSlice.reducer;
