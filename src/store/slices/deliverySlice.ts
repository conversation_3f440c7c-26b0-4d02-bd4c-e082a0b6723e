import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { Delivery, PaginatedResponse } from '@/types';
import { deliveryService } from '@/services/deliveryService';

interface DeliveryState {
  deliveries: Delivery[];
  currentDelivery: Delivery | null;
  totalCount: number;
  currentPage: number;
  pageSize: number;
  loading: boolean;
  error: string | null;
}

const initialState: DeliveryState = {
  deliveries: [],
  currentDelivery: null,
  totalCount: 0,
  currentPage: 1,
  pageSize: 20,
  loading: false,
  error: null,
};

export const fetchDeliveries = createAsyncThunk(
  'delivery/fetchDeliveries',
  async (params: any = {}, { rejectWithValue }) => {
    try {
      const response = await deliveryService.getDeliveries(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取交付列表失败');
    }
  }
);

export const fetchDeliveryById = createAsyncThunk(
  'delivery/fetchDeliveryById',
  async (id: number, { rejectWithValue }) => {
    try {
      const response = await deliveryService.getDeliveryById(id);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取交付详情失败');
    }
  }
);

const deliverySlice = createSlice({
  name: 'delivery',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearCurrentDelivery: (state) => {
      state.currentDelivery = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchDeliveries.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchDeliveries.fulfilled, (state, action) => {
        state.loading = false;
        const response = action.payload as PaginatedResponse<Delivery>;
        state.deliveries = response.content;
        state.totalCount = response.totalElements;
        state.currentPage = response.currentPage;
      })
      .addCase(fetchDeliveries.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchDeliveryById.fulfilled, (state, action) => {
        state.currentDelivery = action.payload;
      });
  },
});

export const { clearError, clearCurrentDelivery } = deliverySlice.actions;
export default deliverySlice.reducer;
