import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface UIState {
  sidebarCollapsed: boolean;
  theme: 'light' | 'dark';
  language: 'zh-CN' | 'en-US';
  loading: {
    global: boolean;
    [key: string]: boolean;
  };
  notifications: {
    visible: boolean;
    count: number;
  };
  breadcrumb: Array<{
    title: string;
    path?: string;
  }>;
}

const initialState: UIState = {
  sidebarCollapsed: false,
  theme: 'light',
  language: 'zh-CN',
  loading: {
    global: false,
  },
  notifications: {
    visible: false,
    count: 0,
  },
  breadcrumb: [],
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    toggleSidebar: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed;
    },
    setSidebarCollapsed: (state, action: PayloadAction<boolean>) => {
      state.sidebarCollapsed = action.payload;
    },
    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {
      state.theme = action.payload;
    },
    setLanguage: (state, action: PayloadAction<'zh-CN' | 'en-US'>) => {
      state.language = action.payload;
    },
    setGlobalLoading: (state, action: PayloadAction<boolean>) => {
      state.loading.global = action.payload;
    },
    setLoading: (state, action: PayloadAction<{ key: string; loading: boolean }>) => {
      state.loading[action.payload.key] = action.payload.loading;
    },
    setNotificationVisible: (state, action: PayloadAction<boolean>) => {
      state.notifications.visible = action.payload;
    },
    setNotificationCount: (state, action: PayloadAction<number>) => {
      state.notifications.count = action.payload;
    },
    setBreadcrumb: (state, action: PayloadAction<Array<{ title: string; path?: string }>>) => {
      state.breadcrumb = action.payload;
    },
    addBreadcrumb: (state, action: PayloadAction<{ title: string; path?: string }>) => {
      state.breadcrumb.push(action.payload);
    },
    clearBreadcrumb: (state) => {
      state.breadcrumb = [];
    },
  },
});

export const {
  toggleSidebar,
  setSidebarCollapsed,
  setTheme,
  setLanguage,
  setGlobalLoading,
  setLoading,
  setNotificationVisible,
  setNotificationCount,
  setBreadcrumb,
  addBreadcrumb,
  clearBreadcrumb,
} = uiSlice.actions;

export default uiSlice.reducer;
