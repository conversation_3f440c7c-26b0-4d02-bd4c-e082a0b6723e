import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { Requirement, PaginatedResponse, RequirementFormData } from '@/types';
import { requirementService } from '@/services/requirementService';

interface RequirementState {
  requirements: Requirement[];
  currentRequirement: Requirement | null;
  totalCount: number;
  currentPage: number;
  pageSize: number;
  loading: boolean;
  error: string | null;
  searchQuery: string;
  filters: {
    status?: string;
    category?: string;
    priority?: string;
    assignedTo?: number;
  };
}

const initialState: RequirementState = {
  requirements: [],
  currentRequirement: null,
  totalCount: 0,
  currentPage: 1,
  pageSize: 20,
  loading: false,
  error: null,
  searchQuery: '',
  filters: {},
};

export const fetchRequirements = createAsyncThunk(
  'requirement/fetchRequirements',
  async (params: any = {}, { rejectWithValue }) => {
    try {
      const response = await requirementService.getRequirements(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取需求列表失败');
    }
  }
);

export const fetchRequirementById = createAsyncThunk(
  'requirement/fetchRequirementById',
  async (id: number, { rejectWithValue }) => {
    try {
      const response = await requirementService.getRequirementById(id);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取需求详情失败');
    }
  }
);

export const createRequirement = createAsyncThunk(
  'requirement/createRequirement',
  async (data: RequirementFormData, { rejectWithValue }) => {
    try {
      const response = await requirementService.createRequirement(data);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '创建需求失败');
    }
  }
);

export const updateRequirement = createAsyncThunk(
  'requirement/updateRequirement',
  async ({ id, data }: { id: number; data: Partial<Requirement> }, { rejectWithValue }) => {
    try {
      const response = await requirementService.updateRequirement(id, data);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '更新需求失败');
    }
  }
);

export const assignRequirement = createAsyncThunk(
  'requirement/assignRequirement',
  async ({ id, userId }: { id: number; userId: number }, { rejectWithValue }) => {
    try {
      const response = await requirementService.assignRequirement(id, userId);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '分配需求失败');
    }
  }
);

const requirementSlice = createSlice({
  name: 'requirement',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setSearchQuery: (state, action) => {
      state.searchQuery = action.payload;
    },
    setFilters: (state, action) => {
      state.filters = action.payload;
    },
    setCurrentPage: (state, action) => {
      state.currentPage = action.payload;
    },
    clearCurrentRequirement: (state) => {
      state.currentRequirement = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchRequirements.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchRequirements.fulfilled, (state, action) => {
        state.loading = false;
        const response = action.payload as PaginatedResponse<Requirement>;
        state.requirements = response.content;
        state.totalCount = response.totalElements;
        state.currentPage = response.currentPage;
      })
      .addCase(fetchRequirements.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchRequirementById.fulfilled, (state, action) => {
        state.currentRequirement = action.payload;
      })
      .addCase(createRequirement.fulfilled, (state, action) => {
        state.requirements.unshift(action.payload);
        state.totalCount += 1;
      })
      .addCase(updateRequirement.fulfilled, (state, action) => {
        const index = state.requirements.findIndex(r => r.id === action.payload.id);
        if (index !== -1) {
          state.requirements[index] = action.payload;
        }
        if (state.currentRequirement?.id === action.payload.id) {
          state.currentRequirement = action.payload;
        }
      });
  },
});

export const {
  clearError,
  setSearchQuery,
  setFilters,
  setCurrentPage,
  clearCurrentRequirement,
} = requirementSlice.actions;

export default requirementSlice.reducer;
