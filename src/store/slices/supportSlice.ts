import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { SupportTicket, SatisfactionSurvey, KnowledgeArticle, PaginatedResponse } from '@/types';
import { supportService } from '@/services/supportService';

interface SupportState {
  tickets: SupportTicket[];
  currentTicket: SupportTicket | null;
  surveys: SatisfactionSurvey[];
  knowledgeArticles: KnowledgeArticle[];
  totalCount: number;
  currentPage: number;
  pageSize: number;
  loading: boolean;
  error: string | null;
}

const initialState: SupportState = {
  tickets: [],
  currentTicket: null,
  surveys: [],
  knowledgeArticles: [],
  totalCount: 0,
  currentPage: 1,
  pageSize: 20,
  loading: false,
  error: null,
};

export const fetchTickets = createAsyncThunk(
  'support/fetchTickets',
  async (params: any = {}, { rejectWithValue }) => {
    try {
      const response = await supportService.getTickets(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取工单列表失败');
    }
  }
);

export const createTicket = createAsyncThunk(
  'support/createTicket',
  async (data: Partial<SupportTicket>, { rejectWithValue }) => {
    try {
      const response = await supportService.createTicket(data);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '创建工单失败');
    }
  }
);

export const fetchKnowledgeArticles = createAsyncThunk(
  'support/fetchKnowledgeArticles',
  async (params: any = {}, { rejectWithValue }) => {
    try {
      const response = await supportService.getKnowledgeArticles(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取知识库失败');
    }
  }
);

const supportSlice = createSlice({
  name: 'support',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearCurrentTicket: (state) => {
      state.currentTicket = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchTickets.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTickets.fulfilled, (state, action) => {
        state.loading = false;
        const response = action.payload as PaginatedResponse<SupportTicket>;
        state.tickets = response.content;
        state.totalCount = response.totalElements;
        state.currentPage = response.currentPage;
      })
      .addCase(fetchTickets.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(createTicket.fulfilled, (state, action) => {
        state.tickets.unshift(action.payload);
        state.totalCount += 1;
      })
      .addCase(fetchKnowledgeArticles.fulfilled, (state, action) => {
        const response = action.payload as PaginatedResponse<KnowledgeArticle>;
        state.knowledgeArticles = response.content;
      });
  },
});

export const { clearError, clearCurrentTicket } = supportSlice.actions;
export default supportSlice.reducer;
