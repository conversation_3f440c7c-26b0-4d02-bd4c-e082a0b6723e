import { configureStore } from '@reduxjs/toolkit';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';

// Import slices
import authSlice from './slices/authSlice';
import customerSlice from './slices/customerSlice';
import requirementSlice from './slices/requirementSlice';
import proposalSlice from './slices/proposalSlice';
import projectSlice from './slices/projectSlice';
import deliverySlice from './slices/deliverySlice';
import supportSlice from './slices/supportSlice';
import notificationSlice from './slices/notificationSlice';
import uiSlice from './slices/uiSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    customer: customerSlice,
    requirement: requirementSlice,
    proposal: proposalSlice,
    project: projectSlice,
    delivery: deliverySlice,
    support: supportSlice,
    notification: notificationSlice,
    ui: uiSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Typed hooks
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
