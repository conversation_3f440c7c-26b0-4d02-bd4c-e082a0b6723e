import { apiRequest } from './api';
import { SupportTicket, SatisfactionSurvey, KnowledgeArticle, PaginatedResponse } from '@/types';

export const supportService = {
  // 工单管理
  getTickets: (params: {
    page?: number;
    size?: number;
    search?: string;
    status?: string;
    category?: string;
    priority?: string;
    customerId?: number;
    assignedTo?: number;
  } = {}) => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });
    return apiRequest.get<PaginatedResponse<SupportTicket>>(`/support/tickets?${queryParams}`);
  },

  getTicketById: (id: number) =>
    apiRequest.get<SupportTicket>(`/support/tickets/${id}`),

  createTicket: (data: Partial<SupportTicket>) =>
    apiRequest.post<SupportTicket>('/support/tickets', data),

  updateTicket: (id: number, data: Partial<SupportTicket>) =>
    apiRequest.put<SupportTicket>(`/support/tickets/${id}`, data),

  assignTicket: (id: number, userId: number) =>
    apiRequest.post(`/support/tickets/${id}/assign`, { userId }),

  resolveTicket: (id: number, resolution: string) =>
    apiRequest.post(`/support/tickets/${id}/resolve`, { resolution }),

  closeTicket: (id: number) =>
    apiRequest.post(`/support/tickets/${id}/close`),

  addTicketComment: (id: number, content: string) =>
    apiRequest.post(`/support/tickets/${id}/comments`, { content }),

  getTicketComments: (id: number) =>
    apiRequest.get(`/support/tickets/${id}/comments`),

  // 满意度调查
  getSurveys: (params: {
    page?: number;
    size?: number;
    surveyType?: string;
    customerId?: number;
    projectId?: number;
  } = {}) => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });
    return apiRequest.get<PaginatedResponse<SatisfactionSurvey>>(`/support/surveys?${queryParams}`);
  },

  createSurvey: (data: Partial<SatisfactionSurvey>) =>
    apiRequest.post<SatisfactionSurvey>('/support/surveys', data),

  submitSurvey: (id: number, data: {
    score: number;
    feedback?: string;
  }) =>
    apiRequest.post(`/support/surveys/${id}/submit`, data),

  getSurveyStats: () =>
    apiRequest.get('/support/surveys/stats'),

  // 知识库管理
  getKnowledgeArticles: (params: {
    page?: number;
    size?: number;
    search?: string;
    category?: string;
    tags?: string[];
  } = {}) => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        if (Array.isArray(value)) {
          value.forEach(v => queryParams.append(key, v));
        } else {
          queryParams.append(key, value.toString());
        }
      }
    });
    return apiRequest.get<PaginatedResponse<KnowledgeArticle>>(`/support/knowledge?${queryParams}`);
  },

  getKnowledgeArticleById: (id: number) =>
    apiRequest.get<KnowledgeArticle>(`/support/knowledge/${id}`),

  createKnowledgeArticle: (data: Partial<KnowledgeArticle>) =>
    apiRequest.post<KnowledgeArticle>('/support/knowledge', data),

  updateKnowledgeArticle: (id: number, data: Partial<KnowledgeArticle>) =>
    apiRequest.put<KnowledgeArticle>(`/support/knowledge/${id}`, data),

  deleteKnowledgeArticle: (id: number) =>
    apiRequest.delete(`/support/knowledge/${id}`),

  incrementViewCount: (id: number) =>
    apiRequest.post(`/support/knowledge/${id}/view`),

  markHelpful: (id: number) =>
    apiRequest.post(`/support/knowledge/${id}/helpful`),

  searchKnowledge: (query: string) =>
    apiRequest.get(`/support/knowledge/search?q=${encodeURIComponent(query)}`),

  getKnowledgeCategories: () =>
    apiRequest.get('/support/knowledge/categories'),

  // FAQ管理
  getFAQs: () =>
    apiRequest.get('/support/faqs'),

  createFAQ: (data: { question: string; answer: string; category: string }) =>
    apiRequest.post('/support/faqs', data),

  updateFAQ: (id: number, data: { question: string; answer: string; category: string }) =>
    apiRequest.put(`/support/faqs/${id}`, data),

  deleteFAQ: (id: number) =>
    apiRequest.delete(`/support/faqs/${id}`),

  // 客户服务统计
  getSupportStats: () =>
    apiRequest.get('/support/stats'),

  getTicketStats: () =>
    apiRequest.get('/support/tickets/stats'),

  getResponseTimeStats: () =>
    apiRequest.get('/support/response-time-stats'),
};
