import { apiRequest } from './api';
import { Delivery, PaginatedResponse } from '@/types';

export const deliveryService = {
  // 获取交付列表
  getDeliveries: (params: {
    page?: number;
    size?: number;
    search?: string;
    status?: string;
    projectId?: number;
    customerId?: number;
  } = {}) => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });
    return apiRequest.get<PaginatedResponse<Delivery>>(`/deliveries?${queryParams}`);
  },

  // 获取交付详情
  getDeliveryById: (id: number) =>
    apiRequest.get<Delivery>(`/deliveries/${id}`),

  // 创建交付
  createDelivery: (data: Partial<Delivery>) =>
    apiRequest.post<Delivery>('/deliveries', data),

  // 更新交付
  updateDelivery: (id: number, data: Partial<Delivery>) =>
    apiRequest.put<Delivery>(`/deliveries/${id}`, data),

  // 删除交付
  deleteDelivery: (id: number) =>
    apiRequest.delete(`/deliveries/${id}`),

  // 更新交付状态
  updateStatus: (id: number, status: string) =>
    apiRequest.post<Delivery>(`/deliveries/${id}/status`, { status }),

  // 获取交付物列表
  getDeliveryItems: (id: number) =>
    apiRequest.get(`/deliveries/${id}/items`),

  // 添加交付物
  addDeliveryItem: (deliveryId: number, data: any) =>
    apiRequest.post(`/deliveries/${deliveryId}/items`, data),

  // 更新交付物
  updateDeliveryItem: (deliveryId: number, itemId: number, data: any) =>
    apiRequest.put(`/deliveries/${deliveryId}/items/${itemId}`, data),

  // 删除交付物
  deleteDeliveryItem: (deliveryId: number, itemId: number) =>
    apiRequest.delete(`/deliveries/${deliveryId}/items/${itemId}`),

  // 上传交付物文件
  uploadDeliveryFile: (deliveryId: number, itemId: number, file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    return apiRequest.post(`/deliveries/${deliveryId}/items/${itemId}/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // 质量检查
  qualityCheck: (deliveryId: number, itemId: number, result: 'PASSED' | 'FAILED', notes?: string) =>
    apiRequest.post(`/deliveries/${deliveryId}/items/${itemId}/quality-check`, {
      result,
      notes,
    }),

  // 发起验收
  initiateAcceptance: (id: number) =>
    apiRequest.post(`/deliveries/${id}/acceptance`),

  // 客户验收
  customerAcceptance: (id: number, data: {
    status: 'ACCEPTED' | 'REJECTED' | 'PARTIAL';
    overallRating?: number;
    feedback?: string;
    issues?: Array<{
      itemId: number;
      description: string;
      severity: 'LOW' | 'MEDIUM' | 'HIGH';
      imageUrl?: string;
    }>;
  }) =>
    apiRequest.post(`/deliveries/${id}/customer-acceptance`, data),

  // 处理验收问题
  resolveAcceptanceIssue: (deliveryId: number, issueId: number, resolution: string) =>
    apiRequest.post(`/deliveries/${deliveryId}/issues/${issueId}/resolve`, { resolution }),

  // 重新交付
  redeliver: (id: number, notes?: string) =>
    apiRequest.post(`/deliveries/${id}/redeliver`, { notes }),

  // 获取验收记录
  getAcceptanceRecord: (id: number) =>
    apiRequest.get(`/deliveries/${id}/acceptance`),

  // 生成交付报告
  generateDeliveryReport: (id: number) =>
    apiRequest.get(`/deliveries/${id}/report`, { responseType: 'blob' }),

  // 获取交付统计
  getDeliveryStats: () =>
    apiRequest.get('/deliveries/stats'),
};
