import { apiRequest } from './api';
import { Customer, PaginatedResponse } from '@/types';

export const customerService = {
  // 获取客户列表
  getCustomers: (params: {
    page?: number;
    size?: number;
    search?: string;
    industry?: string;
    level?: string;
    status?: string;
  } = {}) => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });
    return apiRequest.get<PaginatedResponse<Customer>>(`/customers?${queryParams}`);
  },

  // 获取客户详情
  getCustomerById: (id: number) =>
    apiRequest.get<Customer>(`/customers/${id}`),

  // 创建客户
  createCustomer: (customerData: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>) =>
    apiRequest.post<Customer>('/customers', customerData),

  // 更新客户信息
  updateCustomer: (id: number, customerData: Partial<Customer>) =>
    apiRequest.put<Customer>(`/customers/${id}`, customerData),

  // 删除客户
  deleteCustomer: (id: number) =>
    apiRequest.delete(`/customers/${id}`),

  // 获取客户统计信息
  getCustomerStats: (id: number) =>
    apiRequest.get(`/customers/${id}/stats`),

  // 获取客户项目历史
  getCustomerProjects: (id: number, params: { page?: number; size?: number } = {}) => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });
    return apiRequest.get(`/customers/${id}/projects?${queryParams}`);
  },

  // 获取客户需求历史
  getCustomerRequirements: (id: number, params: { page?: number; size?: number } = {}) => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });
    return apiRequest.get(`/customers/${id}/requirements?${queryParams}`);
  },

  // 导入客户数据
  importCustomers: (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    return apiRequest.post('/customers/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // 导出客户数据
  exportCustomers: (params: any = {}) => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });
    return apiRequest.get(`/customers/export?${queryParams}`, {
      responseType: 'blob',
    });
  },
};
