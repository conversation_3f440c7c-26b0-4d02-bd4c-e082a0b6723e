import { apiRequest } from './api';
import { User, ApiResponse } from '@/types';

export const authService = {
  // 用户登录
  login: (credentials: { username: string; password: string }) =>
    apiRequest.post<{ user: User; token: string }>('/auth/login', credentials),

  // 用户注册
  register: (userData: {
    username: string;
    email: string;
    password: string;
    fullName: string;
    phone?: string;
    userType: 'CUSTOMER' | 'INTERNAL';
  }) =>
    apiRequest.post<{ user: User; token: string }>('/auth/register', userData),

  // 退出登录
  logout: () =>
    apiRequest.post('/auth/logout'),

  // 获取当前用户信息
  getCurrentUser: () =>
    apiRequest.get<User>('/auth/me'),

  // 更新用户信息
  updateProfile: (userData: Partial<User>) =>
    apiRequest.put<User>('/auth/profile', userData),

  // 修改密码
  changePassword: (passwordData: {
    currentPassword: string;
    newPassword: string;
  }) =>
    apiRequest.post('/auth/change-password', passwordData),

  // 忘记密码
  forgotPassword: (email: string) =>
    apiRequest.post('/auth/forgot-password', { email }),

  // 重置密码
  resetPassword: (token: string, newPassword: string) =>
    apiRequest.post('/auth/reset-password', { token, newPassword }),

  // 验证邮箱
  verifyEmail: (token: string) =>
    apiRequest.post('/auth/verify-email', { token }),

  // 刷新token
  refreshToken: () =>
    apiRequest.post<{ token: string }>('/auth/refresh'),
};
