import { apiRequest } from './api';
import { Proposal, ProposalFormData, PaginatedResponse } from '@/types';

export const proposalService = {
  // 获取方案列表
  getProposals: (params: {
    page?: number;
    size?: number;
    search?: string;
    status?: string;
    requirementId?: number;
    createdBy?: number;
  } = {}) => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });
    return apiRequest.get<PaginatedResponse<Proposal>>(`/proposals?${queryParams}`);
  },

  // 获取方案详情
  getProposalById: (id: number) =>
    apiRequest.get<Proposal>(`/proposals/${id}`),

  // 创建方案
  createProposal: (data: ProposalFormData) =>
    apiRequest.post<Proposal>('/proposals', data),

  // 更新方案
  updateProposal: (id: number, data: Partial<Proposal>) =>
    apiRequest.put<Proposal>(`/proposals/${id}`, data),

  // 删除方案
  deleteProposal: (id: number) =>
    apiRequest.delete(`/proposals/${id}`),

  // 提交方案给客户
  submitProposal: (id: number) =>
    apiRequest.post<Proposal>(`/proposals/${id}/submit`),

  // 客户确认方案
  confirmProposal: (id: number) =>
    apiRequest.post<Proposal>(`/proposals/${id}/confirm`),

  // 客户拒绝方案
  rejectProposal: (id: number, reason: string) =>
    apiRequest.post<Proposal>(`/proposals/${id}/reject`, { reason }),

  // 复制方案
  duplicateProposal: (id: number) =>
    apiRequest.post<Proposal>(`/proposals/${id}/duplicate`),

  // 获取方案版本历史
  getProposalVersions: (id: number) =>
    apiRequest.get(`/proposals/${id}/versions`),

  // 添加方案评论
  addComment: (id: number, comment: {
    content: string;
    positionX?: number;
    positionY?: number;
  }) =>
    apiRequest.post(`/proposals/${id}/comments`, comment),

  // 获取方案评论
  getComments: (id: number) =>
    apiRequest.get(`/proposals/${id}/comments`),

  // 回复评论
  replyComment: (proposalId: number, commentId: number, content: string) =>
    apiRequest.post(`/proposals/${proposalId}/comments/${commentId}/reply`, { content }),

  // 解决评论
  resolveComment: (proposalId: number, commentId: number) =>
    apiRequest.post(`/proposals/${proposalId}/comments/${commentId}/resolve`),

  // 获取方案模板
  getTemplates: () =>
    apiRequest.get('/proposals/templates'),

  // 创建方案模板
  createTemplate: (data: any) =>
    apiRequest.post('/proposals/templates', data),

  // 从模板创建方案
  createFromTemplate: (templateId: number, requirementId: number) =>
    apiRequest.post<Proposal>('/proposals/from-template', { templateId, requirementId }),

  // 计算方案成本
  calculateCost: (components: any[]) =>
    apiRequest.post('/proposals/calculate-cost', { components }),

  // 生成方案PDF
  generatePDF: (id: number) =>
    apiRequest.get(`/proposals/${id}/pdf`, { responseType: 'blob' }),

  // 发送方案邮件
  sendEmail: (id: number, emailData: {
    to: string[];
    subject: string;
    message: string;
  }) =>
    apiRequest.post(`/proposals/${id}/send-email`, emailData),

  // 获取方案统计
  getProposalStats: () =>
    apiRequest.get('/proposals/stats'),
};
