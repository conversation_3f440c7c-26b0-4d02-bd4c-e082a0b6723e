import { apiRequest } from './api';
import { Notification, PaginatedResponse } from '@/types';

export const notificationService = {
  // 获取通知列表
  getNotifications: (params: {
    page?: number;
    size?: number;
    type?: string;
    status?: string;
  } = {}) => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });
    return apiRequest.get<PaginatedResponse<Notification>>(`/notifications?${queryParams}`);
  },

  // 获取未读通知数量
  getUnreadCount: () =>
    apiRequest.get<{ count: number }>('/notifications/unread-count'),

  // 标记为已读
  markAsRead: (id: number) =>
    apiRequest.post<Notification>(`/notifications/${id}/read`),

  // 标记全部为已读
  markAllAsRead: () =>
    apiRequest.post('/notifications/read-all'),

  // 删除通知
  deleteNotification: (id: number) =>
    apiRequest.delete(`/notifications/${id}`),

  // 批量删除通知
  deleteNotifications: (ids: number[]) =>
    apiRequest.post('/notifications/batch-delete', { ids }),

  // 获取通知设置
  getNotificationSettings: () =>
    apiRequest.get('/notifications/settings'),

  // 更新通知设置
  updateNotificationSettings: (settings: {
    emailEnabled: boolean;
    smsEnabled: boolean;
    pushEnabled: boolean;
    inAppEnabled: boolean;
    categories: Record<string, boolean>;
  }) =>
    apiRequest.put('/notifications/settings', settings),

  // 发送测试通知
  sendTestNotification: (type: string) =>
    apiRequest.post('/notifications/test', { type }),
};
