import { apiRequest } from './api';
import { Project, PaginatedResponse } from '@/types';

export const projectService = {
  // 获取项目列表
  getProjects: (params: {
    page?: number;
    size?: number;
    search?: string;
    status?: string;
    projectManagerId?: number;
    customerId?: number;
  } = {}) => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });
    return apiRequest.get<PaginatedResponse<Project>>(`/projects?${queryParams}`);
  },

  // 获取项目详情
  getProjectById: (id: number) =>
    apiRequest.get<Project>(`/projects/${id}`),

  // 创建项目
  createProject: (data: Partial<Project>) =>
    apiRequest.post<Project>('/projects', data),

  // 更新项目
  updateProject: (id: number, data: Partial<Project>) =>
    apiRequest.put<Project>(`/projects/${id}`, data),

  // 删除项目
  deleteProject: (id: number) =>
    apiRequest.delete(`/projects/${id}`),

  // 更新项目状态
  updateStatus: (id: number, status: string) =>
    apiRequest.post<Project>(`/projects/${id}/status`, { status }),

  // 获取项目里程碑
  getMilestones: (id: number) =>
    apiRequest.get(`/projects/${id}/milestones`),

  // 创建里程碑
  createMilestone: (projectId: number, data: any) =>
    apiRequest.post(`/projects/${projectId}/milestones`, data),

  // 更新里程碑
  updateMilestone: (projectId: number, milestoneId: number, data: any) =>
    apiRequest.put(`/projects/${projectId}/milestones/${milestoneId}`, data),

  // 获取项目任务
  getTasks: (id: number) =>
    apiRequest.get(`/projects/${id}/tasks`),

  // 创建任务
  createTask: (projectId: number, data: any) =>
    apiRequest.post(`/projects/${projectId}/tasks`, data),

  // 更新任务
  updateTask: (projectId: number, taskId: number, data: any) =>
    apiRequest.put(`/projects/${projectId}/tasks/${taskId}`, data),

  // 分配任务
  assignTask: (projectId: number, taskId: number, userId: number) =>
    apiRequest.post(`/projects/${projectId}/tasks/${taskId}/assign`, { userId }),

  // 获取项目进度
  getProgress: (id: number) =>
    apiRequest.get(`/projects/${id}/progress`),

  // 更新项目进度
  updateProgress: (id: number, progress: number) =>
    apiRequest.post(`/projects/${id}/progress`, { progress }),

  // 获取项目团队
  getTeam: (id: number) =>
    apiRequest.get(`/projects/${id}/team`),

  // 添加团队成员
  addTeamMember: (projectId: number, userId: number, role: string) =>
    apiRequest.post(`/projects/${projectId}/team`, { userId, role }),

  // 移除团队成员
  removeTeamMember: (projectId: number, userId: number) =>
    apiRequest.delete(`/projects/${projectId}/team/${userId}`),

  // 获取项目文件
  getFiles: (id: number) =>
    apiRequest.get(`/projects/${id}/files`),

  // 上传项目文件
  uploadFile: (projectId: number, file: File, category: string) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('category', category);
    return apiRequest.post(`/projects/${projectId}/files`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // 获取项目统计
  getProjectStats: () =>
    apiRequest.get('/projects/stats'),

  // 生成项目报告
  generateReport: (id: number, type: string) =>
    apiRequest.get(`/projects/${id}/report/${type}`, { responseType: 'blob' }),
};
