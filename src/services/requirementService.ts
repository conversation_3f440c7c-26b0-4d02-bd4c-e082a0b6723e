import { apiRequest, uploadFiles } from './api';
import { Requirement, RequirementFormData, PaginatedResponse } from '@/types';

export const requirementService = {
  // 获取需求列表
  getRequirements: (params: {
    page?: number;
    size?: number;
    search?: string;
    status?: string;
    category?: string;
    priority?: string;
    customerId?: number;
    assignedTo?: number;
  } = {}) => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });
    return apiRequest.get<PaginatedResponse<Requirement>>(`/requirements?${queryParams}`);
  },

  // 获取需求详情
  getRequirementById: (id: number) =>
    apiRequest.get<Requirement>(`/requirements/${id}`),

  // 创建需求
  createRequirement: async (data: RequirementFormData) => {
    // 如果有附件，先上传文件
    let attachmentIds: number[] = [];
    if (data.attachments && data.attachments.length > 0) {
      const uploadResponse = await uploadFiles('/files/upload', data.attachments);
      attachmentIds = uploadResponse.data.map((file: any) => file.id);
    }

    const requirementData = {
      ...data,
      attachmentIds,
    };
    delete (requirementData as any).attachments;

    return apiRequest.post<Requirement>('/requirements', requirementData);
  },

  // 更新需求
  updateRequirement: (id: number, data: Partial<Requirement>) =>
    apiRequest.put<Requirement>(`/requirements/${id}`, data),

  // 删除需求
  deleteRequirement: (id: number) =>
    apiRequest.delete(`/requirements/${id}`),

  // 分配需求
  assignRequirement: (id: number, userId: number) =>
    apiRequest.post<Requirement>(`/requirements/${id}/assign`, { userId }),

  // 需求分析
  analyzeRequirement: (id: number) =>
    apiRequest.post(`/requirements/${id}/analyze`),

  // 获取需求标签
  getRequirementTags: (id: number) =>
    apiRequest.get(`/requirements/${id}/tags`),

  // 添加需求评论
  addComment: (id: number, comment: string) =>
    apiRequest.post(`/requirements/${id}/comments`, { content: comment }),

  // 获取需求评论
  getComments: (id: number) =>
    apiRequest.get(`/requirements/${id}/comments`),

  // 获取相似需求
  getSimilarRequirements: (id: number) =>
    apiRequest.get(`/requirements/${id}/similar`),

  // 需求状态变更
  updateStatus: (id: number, status: string, note?: string) =>
    apiRequest.post<Requirement>(`/requirements/${id}/status`, { status, note }),

  // 获取需求统计
  getRequirementStats: () =>
    apiRequest.get('/requirements/stats'),

  // 导出需求数据
  exportRequirements: (params: any = {}) => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });
    return apiRequest.get(`/requirements/export?${queryParams}`, {
      responseType: 'blob',
    });
  },
};
