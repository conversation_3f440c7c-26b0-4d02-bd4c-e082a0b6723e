import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAppSelector } from '@/store';
import { USER_ROLES } from '@/constants';
import LoadingSpinner from './LoadingSpinner';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  allowedRoles?: string[];
  redirectTo?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAuth = true,
  allowedRoles = [],
  redirectTo = '/login',
}) => {
  const location = useLocation();
  const { isAuthenticated, user, loading } = useAppSelector((state) => state.auth);

  // 如果正在加载，显示加载状态
  if (loading) {
    return <LoadingSpinner />;
  }

  // 如果需要认证但用户未登录，重定向到登录页
  if (requireAuth && !isAuthenticated) {
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  // 如果不需要认证，直接渲染子组件
  if (!requireAuth) {
    return <>{children}</>;
  }

  // 如果已登录但没有指定角色要求，直接渲染子组件
  if (allowedRoles.length === 0) {
    return <>{children}</>;
  }

  // 检查用户角色权限
  const userRoles = user?.roles?.map(role => role.name) || [];
  const hasPermission = allowedRoles.some(role => userRoles.includes(role));

  // 如果没有权限，重定向到相应页面
  if (!hasPermission) {
    // 根据用户角色重定向到对应的首页
    const isCustomer = userRoles.includes(USER_ROLES.CUSTOMER);
    const isAdmin = userRoles.some(role => 
      [USER_ROLES.ADMIN, USER_ROLES.SUPER_ADMIN, USER_ROLES.SALES, 
       USER_ROLES.DESIGNER, USER_ROLES.PROJECT_MANAGER, 
       USER_ROLES.DELIVERY_MANAGER, USER_ROLES.CUSTOMER_SERVICE].includes(role)
    );

    if (isCustomer) {
      return <Navigate to="/customer/dashboard" replace />;
    } else if (isAdmin) {
      return <Navigate to="/admin/dashboard" replace />;
    } else {
      return <Navigate to="/login" replace />;
    }
  }

  return <>{children}</>;
};

export default ProtectedRoute;
