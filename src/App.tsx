import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Provider } from 'react-redux';
import { ConfigProvider, App as AntApp } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { store } from '@/store';
import { useAppDispatch, useAppSelector } from '@/store';
import { getCurrentUser } from '@/store/slices/authSlice';
import { ROUTES, STORAGE_KEYS } from '@/constants';

// 导入页面组件
import LoginPage from '@/pages/auth/LoginPage';
import RegisterPage from '@/pages/auth/RegisterPage';
import CustomerLayout from '@/layouts/CustomerLayout';
import AdminLayout from '@/layouts/AdminLayout';
import ProtectedRoute from '@/components/common/ProtectedRoute';
import LoadingSpinner from '@/components/common/LoadingSpinner';

// 客户端页面
import CustomerDashboard from '@/pages/customer/Dashboard';
import CustomerRequirements from '@/pages/customer/Requirements';
import CustomerProposals from '@/pages/customer/Proposals';
import CustomerProjects from '@/pages/customer/Projects';
import CustomerDeliveries from '@/pages/customer/Deliveries';
import CustomerSupport from '@/pages/customer/Support';
import CustomerProfile from '@/pages/customer/Profile';

// 管理端页面
import AdminDashboard from '@/pages/admin/Dashboard';
import AdminCustomers from '@/pages/admin/Customers';
import AdminRequirements from '@/pages/admin/Requirements';
import AdminProposals from '@/pages/admin/Proposals';
import AdminProjects from '@/pages/admin/Projects';
import AdminDeliveries from '@/pages/admin/Deliveries';
import AdminSupport from '@/pages/admin/Support';
import AdminKnowledge from '@/pages/admin/Knowledge';
import AdminAnalytics from '@/pages/admin/Analytics';
import AdminUsers from '@/pages/admin/Users';
import AdminSettings from '@/pages/admin/Settings';

import './App.css';

const AppContent: React.FC = () => {
  const dispatch = useAppDispatch();
  const { isAuthenticated, loading, user } = useAppSelector((state) => state.auth);

  useEffect(() => {
    const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
    if (token && !isAuthenticated) {
      dispatch(getCurrentUser());
    }
  }, [dispatch, isAuthenticated]);

  if (loading) {
    return <LoadingSpinner />;
  }

  const isCustomer = user?.roles.some(role => role.name === 'CUSTOMER');
  const isAdmin = user?.roles.some(role => 
    ['ADMIN', 'SUPER_ADMIN', 'SALES', 'DESIGNER', 'PROJECT_MANAGER', 'DELIVERY_MANAGER', 'CUSTOMER_SERVICE'].includes(role.name)
  );

  return (
    <Router>
      <Routes>
        {/* 公共路由 */}
        <Route path={ROUTES.LOGIN} element={<LoginPage />} />
        <Route path={ROUTES.REGISTER} element={<RegisterPage />} />
        
        {/* 客户端路由 */}
        <Route path="/customer/*" element={
          <ProtectedRoute requireAuth={true} allowedRoles={['CUSTOMER']}>
            <CustomerLayout>
              <Routes>
                <Route path="dashboard" element={<CustomerDashboard />} />
                <Route path="requirements" element={<CustomerRequirements />} />
                <Route path="requirements/:id" element={<CustomerRequirements />} />
                <Route path="proposals" element={<CustomerProposals />} />
                <Route path="proposals/:id" element={<CustomerProposals />} />
                <Route path="projects" element={<CustomerProjects />} />
                <Route path="projects/:id" element={<CustomerProjects />} />
                <Route path="deliveries" element={<CustomerDeliveries />} />
                <Route path="deliveries/:id" element={<CustomerDeliveries />} />
                <Route path="support" element={<CustomerSupport />} />
                <Route path="profile" element={<CustomerProfile />} />
                <Route path="*" element={<Navigate to="/customer/dashboard" replace />} />
              </Routes>
            </CustomerLayout>
          </ProtectedRoute>
        } />

        {/* 管理端路由 */}
        <Route path="/admin/*" element={
          <ProtectedRoute requireAuth={true} allowedRoles={['ADMIN', 'SUPER_ADMIN', 'SALES', 'DESIGNER', 'PROJECT_MANAGER', 'DELIVERY_MANAGER', 'CUSTOMER_SERVICE']}>
            <AdminLayout>
              <Routes>
                <Route path="dashboard" element={<AdminDashboard />} />
                <Route path="customers" element={<AdminCustomers />} />
                <Route path="customers/:id" element={<AdminCustomers />} />
                <Route path="requirements" element={<AdminRequirements />} />
                <Route path="requirements/:id" element={<AdminRequirements />} />
                <Route path="proposals" element={<AdminProposals />} />
                <Route path="proposals/:id" element={<AdminProposals />} />
                <Route path="projects" element={<AdminProjects />} />
                <Route path="projects/:id" element={<AdminProjects />} />
                <Route path="deliveries" element={<AdminDeliveries />} />
                <Route path="deliveries/:id" element={<AdminDeliveries />} />
                <Route path="support" element={<AdminSupport />} />
                <Route path="knowledge" element={<AdminKnowledge />} />
                <Route path="analytics" element={<AdminAnalytics />} />
                <Route path="users" element={<AdminUsers />} />
                <Route path="settings" element={<AdminSettings />} />
                <Route path="*" element={<Navigate to="/admin/dashboard" replace />} />
              </Routes>
            </AdminLayout>
          </ProtectedRoute>
        } />

        {/* 根路由重定向 */}
        <Route path="/" element={
          isAuthenticated ? (
            isCustomer ? <Navigate to="/customer/dashboard" replace /> :
            isAdmin ? <Navigate to="/admin/dashboard" replace /> :
            <Navigate to="/login" replace />
          ) : (
            <Navigate to="/login" replace />
          )
        } />

        {/* 404 页面 */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Router>
  );
};

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <ConfigProvider locale={zhCN}>
        <AntApp>
          <AppContent />
        </AntApp>
      </ConfigProvider>
    </Provider>
  );
};

export default App;
