body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubunt<PERSON>', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* 重置默认样式 */
* {
  box-sizing: border-box;
}

/* Ant Design 样式覆盖 */
.ant-layout {
  background: #f5f5f5;
}

.ant-layout-header {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ant-layout-sider {
  background: #fff;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.ant-menu {
  border-right: none;
}

.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ant-table {
  border-radius: 8px;
  overflow: hidden;
}

.ant-btn {
  border-radius: 6px;
}

.ant-input {
  border-radius: 6px;
}

.ant-select .ant-select-selector {
  border-radius: 6px;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
