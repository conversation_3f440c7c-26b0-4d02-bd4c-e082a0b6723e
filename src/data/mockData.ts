import {
  User,
  Customer,
  Requirement,
  Proposal,
  Project,
  Delivery,
  SupportTicket,
  SatisfactionSurvey,
  KnowledgeArticle,
  FurnitureCategory,
  Material,
  StandardSize,
} from '@/types';

// 家具类别数据
export const furnitureCategories: FurnitureCategory[] = [
  {
    id: 'living_room',
    name: '客厅家具',
    description: '客厅空间的各类家具定制',
    subcategories: [
      {
        id: 'sofa',
        name: '沙发',
        description: '各种风格的定制沙发',
        materials: [],
        standardSizes: [
          { id: 'sofa_s', name: '单人沙发', width: 90, height: 85, depth: 90, unit: 'CM', isCustomizable: true },
          { id: 'sofa_m', name: '双人沙发', width: 150, height: 85, depth: 90, unit: 'CM', isCustomizable: true },
          { id: 'sofa_l', name: '三人沙发', width: 210, height: 85, depth: 90, unit: 'CM', isCustomizable: true },
        ],
      },
      {
        id: 'coffee_table',
        name: '茶几',
        description: '客厅茶几定制',
        materials: [],
        standardSizes: [
          { id: 'table_s', name: '小茶几', width: 80, height: 45, depth: 50, unit: 'CM', isCustomizable: true },
          { id: 'table_m', name: '中茶几', width: 120, height: 45, depth: 60, unit: 'CM', isCustomizable: true },
          { id: 'table_l', name: '大茶几', width: 150, height: 45, depth: 80, unit: 'CM', isCustomizable: true },
        ],
      },
    ],
  },
  {
    id: 'bedroom',
    name: '卧室家具',
    description: '卧室空间的各类家具定制',
    subcategories: [
      {
        id: 'bed',
        name: '床',
        description: '各种尺寸的定制床具',
        materials: [],
        standardSizes: [
          { id: 'bed_single', name: '单人床', width: 120, height: 200, depth: 200, unit: 'CM', isCustomizable: true },
          { id: 'bed_double', name: '双人床', width: 150, height: 200, depth: 200, unit: 'CM', isCustomizable: true },
          { id: 'bed_king', name: '大床', width: 180, height: 200, depth: 200, unit: 'CM', isCustomizable: true },
        ],
      },
      {
        id: 'wardrobe',
        name: '衣柜',
        description: '定制衣柜系统',
        materials: [],
        standardSizes: [
          { id: 'wardrobe_s', name: '小衣柜', width: 120, height: 240, depth: 60, unit: 'CM', isCustomizable: true },
          { id: 'wardrobe_m', name: '中衣柜', width: 180, height: 240, depth: 60, unit: 'CM', isCustomizable: true },
          { id: 'wardrobe_l', name: '大衣柜', width: 240, height: 240, depth: 60, unit: 'CM', isCustomizable: true },
        ],
      },
    ],
  },
];

// 材料数据
export const materials: Material[] = [
  {
    id: 'oak_wood',
    name: '橡木',
    type: 'WOOD',
    grade: 'A级',
    color: '原木色',
    texture: '直纹',
    pricePerUnit: 280,
    unit: '平方米',
    supplier: '北欧木材供应商',
    leadTime: 15,
    imageUrl: '/images/materials/oak.jpg',
  },
  {
    id: 'walnut_wood',
    name: '胡桃木',
    type: 'WOOD',
    grade: 'A级',
    color: '深棕色',
    texture: '山纹',
    pricePerUnit: 450,
    unit: '平方米',
    supplier: '美式木材供应商',
    leadTime: 20,
    imageUrl: '/images/materials/walnut.jpg',
  },
  {
    id: 'genuine_leather',
    name: '头层牛皮',
    type: 'LEATHER',
    grade: '顶级',
    color: '棕色',
    texture: '荔枝纹',
    pricePerUnit: 180,
    unit: '平方米',
    supplier: '意大利皮革供应商',
    leadTime: 25,
    imageUrl: '/images/materials/leather.jpg',
  },
  {
    id: 'linen_fabric',
    name: '亚麻布',
    type: 'FABRIC',
    grade: '优质',
    color: '米白色',
    texture: '平纹',
    pricePerUnit: 85,
    unit: '米',
    supplier: '欧洲纺织供应商',
    leadTime: 10,
    imageUrl: '/images/materials/linen.jpg',
  },
];

// 模拟用户数据
export const mockUsers: User[] = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    fullName: '系统管理员',
    phone: '13800138000',
    avatar: '/images/avatars/admin.jpg',
    roles: [{ id: 1, name: 'ADMIN', description: '系统管理员', permissions: ['*'] }],
    department: '管理部',
    position: '系统管理员',
    status: 'ACTIVE',
    lastLoginAt: '2024-01-15T09:00:00Z',
    createdAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 2,
    username: 'designer_zhang',
    email: '<EMAIL>',
    fullName: '张设计师',
    phone: '13800138001',
    avatar: '/images/avatars/designer.jpg',
    roles: [{ id: 2, name: 'DESIGNER', description: '设计师', permissions: ['proposal:*', 'requirement:read'] }],
    department: '设计部',
    position: '高级设计师',
    status: 'ACTIVE',
    lastLoginAt: '2024-01-15T08:30:00Z',
    createdAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 3,
    username: 'pm_li',
    email: '<EMAIL>',
    fullName: '李项目经理',
    phone: '13800138002',
    avatar: '/images/avatars/pm.jpg',
    roles: [{ id: 3, name: 'PROJECT_MANAGER', description: '项目经理', permissions: ['project:*', 'task:*'] }],
    department: '项目部',
    position: '项目经理',
    status: 'ACTIVE',
    lastLoginAt: '2024-01-15T08:45:00Z',
    createdAt: '2024-01-01T00:00:00Z',
  },
];

// 模拟客户数据
export const mockCustomers: Customer[] = [
  {
    id: 1,
    name: '王先生',
    email: '<EMAIL>',
    phone: '13900139001',
    company: '上海某科技公司',
    industry: '科技',
    level: 'VIP',
    address: '上海市浦东新区张江高科技园区',
    tags: ['高端客户', '回头客', '推荐客户'],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
  },
  {
    id: 2,
    name: '陈女士',
    email: '<EMAIL>',
    phone: '13900139002',
    company: '北京某设计公司',
    industry: '设计',
    level: 'PREMIUM',
    address: '北京市朝阳区三里屯',
    tags: ['设计师', '品味独特', '预算充足'],
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-14T00:00:00Z',
  },
  {
    id: 3,
    name: '刘总',
    email: '<EMAIL>',
    phone: '13900139003',
    company: '深圳某投资公司',
    industry: '金融',
    level: 'VIP',
    address: '深圳市南山区科技园',
    tags: ['企业客户', '大单客户', '长期合作'],
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-13T00:00:00Z',
  },
];

// 模拟需求数据
export const mockRequirements: Requirement[] = [
  {
    id: 1,
    customerId: 1,
    customer: mockCustomers[0],
    title: '现代简约客厅家具定制',
    description: '需要定制一套现代简约风格的客厅家具，包括沙发、茶几、电视柜等。要求环保材料，颜色以白色和原木色为主。',
    category: 'LIVING_ROOM',
    furnitureType: ['SOFA', 'COFFEE_TABLE', 'TV_STAND'],
    budgetRange: '50000-100000',
    expectedDelivery: '2024-03-15',
    priority: 'HIGH',
    status: 'IN_PROGRESS',
    assignedTo: 2,
    assignedUser: mockUsers[1],
    tags: [
      { id: 1, tagName: '风格', tagValue: '现代简约', confidenceScore: 0.95 },
      { id: 2, tagName: '材料', tagValue: '环保', confidenceScore: 0.90 },
      { id: 3, tagName: '颜色', tagValue: '白色+原木', confidenceScore: 0.85 },
    ],
    attachments: [],
    createdAt: '2024-01-10T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
  },
  {
    id: 2,
    customerId: 2,
    customer: mockCustomers[1],
    title: '北欧风格卧室家具套装',
    description: '为新房卧室定制一套北欧风格家具，包括床、衣柜、床头柜、梳妆台。喜欢浅色木材，要求储物功能强大。',
    category: 'BEDROOM',
    furnitureType: ['BED', 'WARDROBE', 'NIGHTSTAND', 'VANITY'],
    budgetRange: '30000-50000',
    expectedDelivery: '2024-04-01',
    priority: 'MEDIUM',
    status: 'SUBMITTED',
    tags: [
      { id: 4, tagName: '风格', tagValue: '北欧', confidenceScore: 0.92 },
      { id: 5, tagName: '功能', tagValue: '储物', confidenceScore: 0.88 },
      { id: 6, tagName: '材料', tagValue: '浅色木材', confidenceScore: 0.90 },
    ],
    attachments: [],
    createdAt: '2024-01-12T00:00:00Z',
    updatedAt: '2024-01-12T00:00:00Z',
  },
];

// 模拟方案数据
export const mockProposals: Proposal[] = [
  {
    id: 1,
    requirementId: 1,
    requirement: mockRequirements[0],
    title: '现代简约客厅家具方案A',
    description: '采用橡木和白色烤漆板材，打造简约而不简单的客厅空间。沙发采用意大利进口头层牛皮，茶几采用钢化玻璃台面。',
    totalCost: 75000,
    profitMargin: 0.25,
    status: 'SENT',
    version: 1,
    components: [
      {
        id: 1,
        componentName: '三人沙发',
        componentType: '沙发',
        material: '头层牛皮+橡木框架',
        dimensions: '210×90×85cm',
        quantity: 1,
        unitCost: 25000,
        totalCost: 25000,
        description: '意大利进口头层牛皮，橡木实木框架，高密度海绵填充',
        imageUrl: '/images/products/sofa_modern.jpg',
        sortOrder: 1,
      },
      {
        id: 2,
        componentName: '茶几',
        componentType: '茶几',
        material: '橡木+钢化玻璃',
        dimensions: '120×60×45cm',
        quantity: 1,
        unitCost: 8000,
        totalCost: 8000,
        description: '橡木实木腿，12mm钢化玻璃台面，简约设计',
        imageUrl: '/images/products/coffee_table_modern.jpg',
        sortOrder: 2,
      },
      {
        id: 3,
        componentName: '电视柜',
        componentType: '电视柜',
        material: '橡木+白色烤漆',
        dimensions: '180×40×50cm',
        quantity: 1,
        unitCost: 12000,
        totalCost: 12000,
        description: '橡木实木框架，白色烤漆面板，隐藏式走线设计',
        imageUrl: '/images/products/tv_stand_modern.jpg',
        sortOrder: 3,
      },
    ],
    comments: [],
    createdBy: 2,
    createdUser: mockUsers[1],
    createdAt: '2024-01-11T00:00:00Z',
    updatedAt: '2024-01-14T00:00:00Z',
  },
];

// 模拟项目数据
export const mockProjects: Project[] = [
  {
    id: 1,
    proposalId: 1,
    proposal: mockProposals[0],
    name: '王先生客厅家具定制项目',
    description: '现代简约风格客厅家具定制项目，包括沙发、茶几、电视柜的设计、生产和安装。',
    startDate: '2024-01-15',
    endDate: '2024-03-15',
    actualStartDate: '2024-01-15',
    status: 'IN_PROGRESS',
    progressPercentage: 35,
    projectManagerId: 3,
    projectManager: mockUsers[2],
    milestones: [
      {
        id: 1,
        projectId: 1,
        name: '设计确认',
        description: '完成详细设计图纸并获得客户确认',
        plannedDate: '2024-01-25',
        actualDate: '2024-01-24',
        status: 'COMPLETED',
        sortOrder: 1,
        isCustomerVisible: true,
      },
      {
        id: 2,
        projectId: 1,
        name: '材料采购',
        description: '采购所需的木材、皮革等原材料',
        plannedDate: '2024-02-05',
        status: 'IN_PROGRESS',
        sortOrder: 2,
        isCustomerVisible: true,
      },
      {
        id: 3,
        projectId: 1,
        name: '生产制作',
        description: '工厂生产制作家具',
        plannedDate: '2024-02-28',
        status: 'PENDING',
        sortOrder: 3,
        isCustomerVisible: true,
      },
      {
        id: 4,
        projectId: 1,
        name: '质检包装',
        description: '质量检查和包装',
        plannedDate: '2024-03-10',
        status: 'PENDING',
        sortOrder: 4,
        isCustomerVisible: true,
      },
      {
        id: 5,
        projectId: 1,
        name: '配送安装',
        description: '配送到客户家中并完成安装',
        plannedDate: '2024-03-15',
        status: 'PENDING',
        sortOrder: 5,
        isCustomerVisible: true,
      },
    ],
    tasks: [],
    createdAt: '2024-01-15T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
  },
];

// 模拟交付数据
export const mockDeliveries: Delivery[] = [
  {
    id: 1,
    projectId: 1,
    project: mockProjects[0],
    deliveryDate: '2024-03-15',
    status: 'PREPARING',
    notes: '正在准备交付物，预计按时完成',
    deliveredBy: 3,
    deliveredUser: mockUsers[2],
    items: [
      {
        id: 1,
        deliveryId: 1,
        itemName: '三人沙发',
        itemType: '家具',
        status: 'PENDING',
        qualityCheckStatus: 'PENDING',
        description: '现代简约风格三人沙发，头层牛皮材质',
        imageUrl: '/images/products/sofa_modern.jpg',
      },
      {
        id: 2,
        deliveryId: 1,
        itemName: '茶几',
        itemType: '家具',
        status: 'PENDING',
        qualityCheckStatus: 'PENDING',
        description: '橡木茶几，钢化玻璃台面',
        imageUrl: '/images/products/coffee_table_modern.jpg',
      },
    ],
    createdAt: '2024-01-15T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
  },
];

// 模拟工单数据
export const mockSupportTickets: SupportTicket[] = [
  {
    id: 1,
    customerId: 1,
    customer: mockCustomers[0],
    projectId: 1,
    project: mockProjects[0],
    title: '沙发颜色咨询',
    description: '想了解一下沙发皮革的颜色选择，是否可以提供色卡参考？',
    category: '产品咨询',
    priority: 'MEDIUM',
    status: 'OPEN',
    createdAt: '2024-01-14T00:00:00Z',
  },
  {
    id: 2,
    customerId: 2,
    customer: mockCustomers[1],
    title: '配送时间确认',
    description: '请确认一下家具的具体配送时间，需要提前安排时间接收。',
    category: '配送问题',
    priority: 'HIGH',
    status: 'IN_PROGRESS',
    assignedTo: 3,
    assignedUser: mockUsers[2],
    createdAt: '2024-01-13T00:00:00Z',
  },
];

// 模拟满意度调查数据
export const mockSatisfactionSurveys: SatisfactionSurvey[] = [
  {
    id: 1,
    projectId: 1,
    project: mockProjects[0],
    customerId: 1,
    customer: mockCustomers[0],
    surveyType: 'NPS',
    score: 9,
    feedback: '设计师很专业，沟通顺畅，期待最终成品！',
    surveyDate: '2024-01-25',
    responseDate: '2024-01-26',
  },
];

// 模拟知识库数据
export const mockKnowledgeArticles: KnowledgeArticle[] = [
  {
    id: 1,
    title: '如何选择合适的沙发尺寸？',
    content: '选择沙发尺寸需要考虑客厅空间大小、使用人数、摆放位置等因素...',
    category: '产品选择',
    tags: ['沙发', '尺寸', '选择指南'],
    viewCount: 156,
    helpfulCount: 23,
    createdBy: 2,
    createdUser: mockUsers[1],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-10T00:00:00Z',
  },
  {
    id: 2,
    title: '实木家具保养指南',
    content: '实木家具需要定期保养才能保持良好状态，以下是详细的保养方法...',
    category: '保养维护',
    tags: ['实木', '保养', '维护'],
    viewCount: 89,
    helpfulCount: 15,
    createdBy: 2,
    createdUser: mockUsers[1],
    createdAt: '2024-01-05T00:00:00Z',
    updatedAt: '2024-01-12T00:00:00Z',
  },
];

// 仪表盘统计数据
export const mockDashboardStats = {
  totalCustomers: 156,
  activeProjects: 23,
  completedProjects: 89,
  averageNPS: 8.5,
  monthlyRevenue: 1250000,
  customerSatisfaction: 4.6,
  onTimeDeliveryRate: 0.92,
  pendingRequirements: 12,
};

// 图表数据
export const mockChartData = {
  monthlyRevenue: [
    { name: '1月', value: 850000 },
    { name: '2月', value: 920000 },
    { name: '3月', value: 1100000 },
    { name: '4月', value: 980000 },
    { name: '5月', value: 1250000 },
    { name: '6月', value: 1180000 },
  ],
  projectStatus: [
    { name: '进行中', value: 23 },
    { name: '已完成', value: 89 },
    { name: '已延期', value: 5 },
    { name: '已取消', value: 2 },
  ],
  customerSatisfaction: [
    { name: '非常满意', value: 45 },
    { name: '满意', value: 38 },
    { name: '一般', value: 12 },
    { name: '不满意', value: 3 },
    { name: '非常不满意', value: 2 },
  ],
};
