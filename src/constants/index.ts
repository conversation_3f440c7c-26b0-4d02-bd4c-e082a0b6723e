// API 基础配置
export const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080/api';

// 路由常量
export const ROUTES = {
  // 公共路由
  HOME: '/',
  LOGIN: '/login',
  REGISTER: '/register',
  
  // 客户端路由
  CUSTOMER: {
    DASHBOARD: '/customer/dashboard',
    REQUIREMENTS: '/customer/requirements',
    REQUIREMENT_DETAIL: '/customer/requirements/:id',
    PROPOSALS: '/customer/proposals',
    PROPOSAL_DETAIL: '/customer/proposals/:id',
    PROJECTS: '/customer/projects',
    PROJECT_DETAIL: '/customer/projects/:id',
    DELIVERIES: '/customer/deliveries',
    DELIVERY_DETAIL: '/customer/deliveries/:id',
    SUPPORT: '/customer/support',
    PROFILE: '/customer/profile',
  },
  
  // 内部管理端路由
  ADMIN: {
    DASHBOARD: '/admin/dashboard',
    CUSTOMERS: '/admin/customers',
    CUSTOMER_DETAIL: '/admin/customers/:id',
    REQUIREMENTS: '/admin/requirements',
    REQUIREMENT_DETAIL: '/admin/requirements/:id',
    PROPOSALS: '/admin/proposals',
    PROPOSAL_DETAIL: '/admin/proposals/:id',
    PROJECTS: '/admin/projects',
    PROJECT_DETAIL: '/admin/projects/:id',
    DELIVERIES: '/admin/deliveries',
    DELIVERY_DETAIL: '/admin/deliveries/:id',
    SUPPORT: '/admin/support',
    KNOWLEDGE: '/admin/knowledge',
    ANALYTICS: '/admin/analytics',
    USERS: '/admin/users',
    SETTINGS: '/admin/settings',
  }
} as const;

// 用户角色常量
export const USER_ROLES = {
  CUSTOMER: 'CUSTOMER',
  SALES: 'SALES',
  DESIGNER: 'DESIGNER',
  PROJECT_MANAGER: 'PROJECT_MANAGER',
  DELIVERY_MANAGER: 'DELIVERY_MANAGER',
  CUSTOMER_SERVICE: 'CUSTOMER_SERVICE',
  ADMIN: 'ADMIN',
  SUPER_ADMIN: 'SUPER_ADMIN'
} as const;

// 状态常量
export const STATUS = {
  REQUIREMENT: {
    SUBMITTED: 'SUBMITTED',
    ANALYZING: 'ANALYZING',
    ASSIGNED: 'ASSIGNED',
    IN_PROGRESS: 'IN_PROGRESS',
    COMPLETED: 'COMPLETED'
  },
  PROPOSAL: {
    DRAFT: 'DRAFT',
    REVIEWING: 'REVIEWING',
    SENT: 'SENT',
    CONFIRMED: 'CONFIRMED',
    REJECTED: 'REJECTED'
  },
  PROJECT: {
    PLANNING: 'PLANNING',
    IN_PROGRESS: 'IN_PROGRESS',
    DELAYED: 'DELAYED',
    COMPLETED: 'COMPLETED',
    CANCELLED: 'CANCELLED'
  },
  TASK: {
    NOT_STARTED: 'NOT_STARTED',
    IN_PROGRESS: 'IN_PROGRESS',
    COMPLETED: 'COMPLETED',
    DELAYED: 'DELAYED',
    CANCELLED: 'CANCELLED'
  },
  DELIVERY: {
    PREPARING: 'PREPARING',
    READY: 'READY',
    DELIVERED: 'DELIVERED',
    ACCEPTED: 'ACCEPTED',
    REJECTED: 'REJECTED'
  },
  TICKET: {
    OPEN: 'OPEN',
    IN_PROGRESS: 'IN_PROGRESS',
    RESOLVED: 'RESOLVED',
    CLOSED: 'CLOSED'
  }
} as const;

// 优先级常量
export const PRIORITY = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  URGENT: 'URGENT'
} as const;

// 家具类别常量
export const FURNITURE_CATEGORIES = {
  LIVING_ROOM: 'LIVING_ROOM',
  BEDROOM: 'BEDROOM',
  KITCHEN: 'KITCHEN',
  DINING_ROOM: 'DINING_ROOM',
  OFFICE: 'OFFICE',
  BATHROOM: 'BATHROOM',
  OUTDOOR: 'OUTDOOR',
  CUSTOM: 'CUSTOM'
} as const;

// 家具类型常量
export const FURNITURE_TYPES = {
  LIVING_ROOM: [
    'SOFA', 'COFFEE_TABLE', 'TV_STAND', 'BOOKSHELF', 'SIDE_TABLE', 'ARMCHAIR'
  ],
  BEDROOM: [
    'BED', 'WARDROBE', 'DRESSER', 'NIGHTSTAND', 'CHEST_OF_DRAWERS', 'VANITY'
  ],
  KITCHEN: [
    'CABINET', 'ISLAND', 'PANTRY', 'BREAKFAST_BAR', 'WINE_RACK'
  ],
  DINING_ROOM: [
    'DINING_TABLE', 'DINING_CHAIR', 'BUFFET', 'CHINA_CABINET', 'BAR_CART'
  ],
  OFFICE: [
    'DESK', 'OFFICE_CHAIR', 'FILING_CABINET', 'BOOKCASE', 'CONFERENCE_TABLE'
  ],
  BATHROOM: [
    'VANITY', 'MEDICINE_CABINET', 'LINEN_CABINET', 'STORAGE_BENCH'
  ],
  OUTDOOR: [
    'PATIO_SET', 'OUTDOOR_SOFA', 'GARDEN_BENCH', 'OUTDOOR_DINING_TABLE'
  ]
} as const;

// 材料类型常量
export const MATERIAL_TYPES = {
  WOOD: 'WOOD',
  METAL: 'METAL',
  FABRIC: 'FABRIC',
  LEATHER: 'LEATHER',
  GLASS: 'GLASS',
  PLASTIC: 'PLASTIC',
  STONE: 'STONE',
  COMPOSITE: 'COMPOSITE'
} as const;

// 木材类型
export const WOOD_TYPES = [
  'OAK', 'MAPLE', 'CHERRY', 'WALNUT', 'PINE', 'BIRCH', 'MAHOGANY', 'TEAK', 'BAMBOO'
];

// 金属类型
export const METAL_TYPES = [
  'STEEL', 'ALUMINUM', 'BRASS', 'COPPER', 'IRON', 'STAINLESS_STEEL'
];

// 面料类型
export const FABRIC_TYPES = [
  'COTTON', 'LINEN', 'WOOL', 'SILK', 'POLYESTER', 'VELVET', 'MICROFIBER'
];

// 皮革类型
export const LEATHER_TYPES = [
  'GENUINE_LEATHER', 'TOP_GRAIN', 'FULL_GRAIN', 'BONDED_LEATHER', 'FAUX_LEATHER'
];

// 预算范围常量
export const BUDGET_RANGES = [
  { value: '0-5000', label: '5,000元以下' },
  { value: '5000-10000', label: '5,000-10,000元' },
  { value: '10000-20000', label: '10,000-20,000元' },
  { value: '20000-50000', label: '20,000-50,000元' },
  { value: '50000-100000', label: '50,000-100,000元' },
  { value: '100000+', label: '100,000元以上' }
];

// 客户等级常量
export const CUSTOMER_LEVELS = {
  STANDARD: 'STANDARD',
  PREMIUM: 'PREMIUM',
  VIP: 'VIP'
} as const;

// 通知类型常量
export const NOTIFICATION_TYPES = {
  EMAIL: 'EMAIL',
  SMS: 'SMS',
  PUSH: 'PUSH',
  IN_APP: 'IN_APP'
} as const;

// 文件类型常量
export const FILE_TYPES = {
  IMAGE: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
  DOCUMENT: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
  CAD: ['dwg', 'dxf', 'step', 'iges'],
  VIDEO: ['mp4', 'avi', 'mov', 'wmv'],
  ARCHIVE: ['zip', 'rar', '7z']
};

// 最大文件大小 (字节)
export const MAX_FILE_SIZE = {
  IMAGE: 10 * 1024 * 1024, // 10MB
  DOCUMENT: 50 * 1024 * 1024, // 50MB
  CAD: 100 * 1024 * 1024, // 100MB
  VIDEO: 500 * 1024 * 1024, // 500MB
  ARCHIVE: 200 * 1024 * 1024 // 200MB
};

// 分页配置
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  PAGE_SIZE_OPTIONS: ['10', '20', '50', '100']
};

// 日期格式
export const DATE_FORMATS = {
  DATE: 'YYYY-MM-DD',
  DATETIME: 'YYYY-MM-DD HH:mm:ss',
  TIME: 'HH:mm:ss',
  DISPLAY_DATE: 'YYYY年MM月DD日',
  DISPLAY_DATETIME: 'YYYY年MM月DD日 HH:mm'
};

// 颜色主题
export const THEME_COLORS = {
  PRIMARY: '#1890ff',
  SUCCESS: '#52c41a',
  WARNING: '#faad14',
  ERROR: '#f5222d',
  INFO: '#1890ff'
};

// 状态颜色映射
export const STATUS_COLORS = {
  SUBMITTED: '#108ee9',
  ANALYZING: '#87d068',
  ASSIGNED: '#2db7f5',
  IN_PROGRESS: '#faad14',
  COMPLETED: '#52c41a',
  DRAFT: '#d9d9d9',
  REVIEWING: '#faad14',
  SENT: '#108ee9',
  CONFIRMED: '#52c41a',
  REJECTED: '#f5222d',
  PLANNING: '#d9d9d9',
  DELAYED: '#f5222d',
  CANCELLED: '#8c8c8c',
  NOT_STARTED: '#d9d9d9',
  PREPARING: '#faad14',
  READY: '#52c41a',
  DELIVERED: '#108ee9',
  ACCEPTED: '#52c41a',
  OPEN: '#f5222d',
  RESOLVED: '#52c41a',
  CLOSED: '#8c8c8c'
};

// 本地存储键名
export const STORAGE_KEYS = {
  TOKEN: 'auth_token',
  USER: 'user_info',
  THEME: 'theme_preference',
  LANGUAGE: 'language_preference'
};

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  UNAUTHORIZED: '登录已过期，请重新登录',
  FORBIDDEN: '您没有权限执行此操作',
  NOT_FOUND: '请求的资源不存在',
  SERVER_ERROR: '服务器内部错误，请稍后重试',
  VALIDATION_ERROR: '输入数据格式不正确',
  FILE_TOO_LARGE: '文件大小超出限制',
  UNSUPPORTED_FILE_TYPE: '不支持的文件类型'
};

// 成功消息
export const SUCCESS_MESSAGES = {
  SAVE_SUCCESS: '保存成功',
  UPDATE_SUCCESS: '更新成功',
  DELETE_SUCCESS: '删除成功',
  SUBMIT_SUCCESS: '提交成功',
  UPLOAD_SUCCESS: '上传成功',
  LOGIN_SUCCESS: '登录成功',
  LOGOUT_SUCCESS: '退出成功'
};
