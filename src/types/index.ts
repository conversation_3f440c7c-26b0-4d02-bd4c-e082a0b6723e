// 用户相关类型定义
export interface User {
  id: number;
  username: string;
  email: string;
  fullName: string;
  phone?: string;
  avatar?: string;
  roles: UserRole[];
  department?: string;
  position?: string;
  status: 'ACTIVE' | 'INACTIVE';
  lastLoginAt?: string;
  createdAt: string;
}

export interface UserRole {
  id: number;
  name: string;
  description: string;
  permissions: string[];
}

// 客户相关类型定义
export interface Customer {
  id: number;
  name: string;
  email: string;
  phone: string;
  company?: string;
  industry: string;
  level: 'VIP' | 'PREMIUM' | 'STANDARD';
  address?: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

// 需求相关类型定义
export interface Requirement {
  id: number;
  customerId: number;
  customer?: Customer;
  title: string;
  description: string;
  category: 'LIVING_ROOM' | 'BEDROOM' | 'KITCHEN' | 'OFFICE' | 'CUSTOM';
  furnitureType: string[];
  budgetRange: string;
  expectedDelivery: string;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  status: 'SUBMITTED' | 'ANALYZING' | 'ASSIGNED' | 'IN_PROGRESS' | 'COMPLETED';
  assignedTo?: number;
  assignedUser?: User;
  tags: RequirementTag[];
  attachments: FileInfo[];
  createdAt: string;
  updatedAt: string;
}

export interface RequirementTag {
  id: number;
  tagName: string;
  tagValue: string;
  confidenceScore: number;
}

// 方案相关类型定义
export interface Proposal {
  id: number;
  requirementId: number;
  requirement?: Requirement;
  title: string;
  description: string;
  totalCost: number;
  profitMargin: number;
  status: 'DRAFT' | 'REVIEWING' | 'SENT' | 'CONFIRMED' | 'REJECTED';
  version: number;
  components: ProposalComponent[];
  comments: ProposalComment[];
  createdBy: number;
  createdUser?: User;
  createdAt: string;
  updatedAt: string;
}

export interface ProposalComponent {
  id: number;
  componentName: string;
  componentType: string;
  material: string;
  dimensions: string;
  quantity: number;
  unitCost: number;
  totalCost: number;
  description?: string;
  imageUrl?: string;
  sortOrder: number;
}

export interface ProposalComment {
  id: number;
  userId: number;
  user?: User;
  userType: 'CUSTOMER' | 'INTERNAL';
  content: string;
  positionX?: number;
  positionY?: number;
  status: 'OPEN' | 'RESOLVED';
  createdAt: string;
}

// 项目相关类型定义
export interface Project {
  id: number;
  proposalId: number;
  proposal?: Proposal;
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  actualStartDate?: string;
  actualEndDate?: string;
  status: 'PLANNING' | 'IN_PROGRESS' | 'DELAYED' | 'COMPLETED' | 'CANCELLED';
  progressPercentage: number;
  projectManagerId: number;
  projectManager?: User;
  milestones: ProjectMilestone[];
  tasks: ProjectTask[];
  createdAt: string;
  updatedAt: string;
}

export interface ProjectMilestone {
  id: number;
  projectId: number;
  name: string;
  description: string;
  plannedDate: string;
  actualDate?: string;
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'DELAYED';
  sortOrder: number;
  isCustomerVisible: boolean;
}

export interface ProjectTask {
  id: number;
  projectId: number;
  milestoneId?: number;
  name: string;
  description: string;
  assignedTo?: number;
  assignedUser?: User;
  plannedStartDate: string;
  plannedEndDate: string;
  actualStartDate?: string;
  actualEndDate?: string;
  status: 'NOT_STARTED' | 'IN_PROGRESS' | 'COMPLETED' | 'DELAYED' | 'CANCELLED';
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  estimatedHours: number;
  actualHours?: number;
  dependencies: number[];
}

// 交付相关类型定义
export interface Delivery {
  id: number;
  projectId: number;
  project?: Project;
  deliveryDate: string;
  status: 'PREPARING' | 'READY' | 'DELIVERED' | 'ACCEPTED' | 'REJECTED';
  notes?: string;
  deliveredBy: number;
  deliveredUser?: User;
  items: DeliveryItem[];
  acceptanceRecord?: AcceptanceRecord;
  createdAt: string;
  updatedAt: string;
}

export interface DeliveryItem {
  id: number;
  deliveryId: number;
  itemName: string;
  itemType: string;
  filePath?: string;
  fileSize?: number;
  checksum?: string;
  status: 'PENDING' | 'READY' | 'DELIVERED';
  qualityCheckStatus: 'PENDING' | 'PASSED' | 'FAILED';
  description?: string;
  imageUrl?: string;
}

export interface AcceptanceRecord {
  id: number;
  deliveryId: number;
  customerId: number;
  customer?: Customer;
  acceptanceDate?: string;
  status: 'PENDING' | 'ACCEPTED' | 'REJECTED' | 'PARTIAL';
  overallRating?: number;
  feedback?: string;
  issuesFound: number;
  issues: AcceptanceIssue[];
  createdAt: string;
}

export interface AcceptanceIssue {
  id: number;
  acceptanceRecordId: number;
  itemId: number;
  description: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH';
  status: 'OPEN' | 'RESOLVED';
  imageUrl?: string;
  resolution?: string;
  resolvedAt?: string;
}

// 客户服务相关类型定义
export interface SatisfactionSurvey {
  id: number;
  projectId: number;
  project?: Project;
  customerId: number;
  customer?: Customer;
  surveyType: 'NPS' | 'CSAT' | 'CES';
  score: number;
  feedback?: string;
  surveyDate: string;
  responseDate?: string;
}

export interface SupportTicket {
  id: number;
  customerId: number;
  customer?: Customer;
  projectId?: number;
  project?: Project;
  title: string;
  description: string;
  category: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  status: 'OPEN' | 'IN_PROGRESS' | 'RESOLVED' | 'CLOSED';
  assignedTo?: number;
  assignedUser?: User;
  resolution?: string;
  createdAt: string;
  resolvedAt?: string;
}

export interface KnowledgeArticle {
  id: number;
  title: string;
  content: string;
  category: string;
  tags: string[];
  viewCount: number;
  helpfulCount: number;
  createdBy: number;
  createdUser?: User;
  createdAt: string;
  updatedAt: string;
}

// 文件相关类型定义
export interface FileInfo {
  id: number;
  originalName: string;
  storedName: string;
  filePath: string;
  fileSize: number;
  mimeType: string;
  checksum: string;
  uploadedBy: number;
  uploadedUser?: User;
  uploadDate: string;
  isDeleted: boolean;
}

// 通知相关类型定义
export interface Notification {
  id: number;
  recipientId: number;
  subject: string;
  content: string;
  type: 'EMAIL' | 'SMS' | 'PUSH' | 'IN_APP';
  status: 'PENDING' | 'SENT' | 'FAILED' | 'READ';
  sentAt?: string;
  readAt?: string;
  createdAt: string;
}

// API响应类型定义
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  code?: string;
}

export interface PaginatedResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
}

// 表单相关类型定义
export interface RequirementFormData {
  title: string;
  description: string;
  category: string;
  furnitureType: string[];
  budgetRange: string;
  expectedDelivery: string;
  priority: string;
  attachments: File[];
  customerInfo?: {
    name: string;
    email: string;
    phone: string;
    company?: string;
    address?: string;
  };
}

export interface ProposalFormData {
  title: string;
  description: string;
  components: Omit<ProposalComponent, 'id'>[];
  totalCost: number;
  profitMargin: number;
}

// 统计数据类型定义
export interface DashboardStats {
  totalCustomers: number;
  activeProjects: number;
  completedProjects: number;
  averageNPS: number;
  monthlyRevenue: number;
  customerSatisfaction: number;
  onTimeDeliveryRate: number;
  pendingRequirements: number;
}

export interface ChartData {
  name: string;
  value: number;
  date?: string;
}

// 家具行业特定类型定义
export interface FurnitureCategory {
  id: string;
  name: string;
  description: string;
  subcategories: FurnitureSubcategory[];
}

export interface FurnitureSubcategory {
  id: string;
  name: string;
  description: string;
  materials: Material[];
  standardSizes: StandardSize[];
}

export interface Material {
  id: string;
  name: string;
  type: 'WOOD' | 'METAL' | 'FABRIC' | 'LEATHER' | 'GLASS' | 'PLASTIC';
  grade: string;
  color: string;
  texture: string;
  pricePerUnit: number;
  unit: string;
  supplier: string;
  leadTime: number;
  imageUrl?: string;
}

export interface StandardSize {
  id: string;
  name: string;
  width: number;
  height: number;
  depth: number;
  unit: 'CM' | 'INCH';
  isCustomizable: boolean;
}

export interface CustomizationOption {
  id: string;
  name: string;
  type: 'COLOR' | 'SIZE' | 'MATERIAL' | 'STYLE' | 'FEATURE';
  options: string[];
  additionalCost: number;
  description?: string;
}
